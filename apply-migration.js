// A simpler script to apply the migration
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the migration file
const migrationPath = path.join(__dirname, 'supabase/migrations/20240620000000_fix_user_profiles_and_subscriptions.sql');
const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

console.log('=== SQL Migration to Run in Supabase SQL Editor ===');
console.log(migrationSQL);
console.log('\n=== Instructions ===');
console.log('1. Copy the SQL above');
console.log('2. Go to your Supabase project dashboard');
console.log('3. Click on "SQL Editor" in the left sidebar');
console.log('4. Create a "New Query"');
console.log('5. Paste the SQL and click "Run"');
console.log('6. Restart your application');

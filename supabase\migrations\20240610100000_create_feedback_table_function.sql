-- Create a function to create the feedback table
CREATE OR REPLACE FUNCTION create_feedback_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create feedback table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.feedback (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT NOT NULL,
    message TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'new',
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
  );
  
  -- Enable Row Level Security
  ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;
  
  -- Create policies for feedback
  -- Drop existing policies if they exist
  DROP POLICY IF EXISTS "Anyone can submit feedback" ON public.feedback;
  DROP POLICY IF EXISTS "Users can view their own feedback" ON public.feedback;
  DROP POLICY IF EXISTS "Ad<PERSON> can view all feedback" ON public.feedback;
  DROP POLICY IF EXISTS "<PERSON><PERSON> can update feedback" ON public.feedback;
  
  -- Create new policies
  CREATE POLICY "Anyone can submit feedback"
    ON public.feedback
    FOR INSERT
    WITH CHECK (true);
  
  CREATE POLICY "Users can view their own feedback"
    ON public.feedback
    FOR SELECT
    USING (auth.uid() = user_id OR user_id IS NULL);
  
  -- Create indexes for faster queries
  CREATE INDEX IF NOT EXISTS idx_feedback_status ON public.feedback(status);
  CREATE INDEX IF NOT EXISTS idx_feedback_user_id ON public.feedback(user_id);
  CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON public.feedback(created_at);
END;
$$;

import { supabase } from '@/integrations/supabase/client';
import { User } from '@supabase/supabase-js';

export interface FeedbackData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

/**
 * Submit feedback to the database
 * @param data Feedback data (name, email, subject, message)
 * @param user Current user (optional)
 * @returns Object with success status and error message if applicable
 */
export async function submitFeedback(data: FeedbackData, user: User | null = null) {
  try {
    // Validate input
    if (!data.name.trim()) {
      return { success: false, error: 'Name is required' };
    }

    if (!data.email.trim()) {
      return { success: false, error: 'Email is required' };
    }

    if (!data.subject.trim()) {
      return { success: false, error: 'Subject is required' };
    }

    if (!data.message.trim()) {
      return { success: false, error: 'Message is required' };
    }

    console.log('Submitting feedback:', { name: data.name, email: data.email, subject: data.subject });

    // Create the feedback item
    const feedbackItem = {
      id: generateUUID(),
      name: data.name,
      email: data.email,
      subject: data.subject,
      message: data.message,
      user_id: user?.id || null,
      status: 'new',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // First try to save to local storage to ensure we don't lose the feedback
    let savedToLocal = false;
    try {
      // Get existing feedback from local storage
      const existingFeedback = localStorage.getItem('pendingFeedback');
      const feedbackArray = existingFeedback ? JSON.parse(existingFeedback) : [];

      // Add new feedback and save back to local storage
      feedbackArray.push(feedbackItem);
      localStorage.setItem('pendingFeedback', JSON.stringify(feedbackArray));

      console.log('Feedback saved to local storage');
      savedToLocal = true;
    } catch (localError: any) {
      console.error('Error saving to local storage:', localError);
      // Continue anyway - we'll try the database
    }

    // Now try to insert into the database
    let savedToDatabase = false;
    try {
      const { error } = await supabase
        .from('feedback')
        .insert({
          name: data.name,
          email: data.email,
          subject: data.subject,
          message: data.message,
          user_id: user?.id || null,
          status: 'new'
        });

      if (!error) {
        console.log('Feedback saved to database');
        savedToDatabase = true;

        // If we successfully saved to the database, we can remove from local storage
        // to avoid duplicates, but only if we know the local storage is working
        if (savedToLocal) {
          try {
            const existingFeedback = localStorage.getItem('pendingFeedback');
            if (existingFeedback) {
              const feedbackArray = JSON.parse(existingFeedback);
              // Remove the item we just saved to the database
              const updatedArray = feedbackArray.filter(item => item.id !== feedbackItem.id);
              localStorage.setItem('pendingFeedback', JSON.stringify(updatedArray));
            }
          } catch (cleanupError) {
            console.error('Error cleaning up local storage:', cleanupError);
            // Not critical, so continue
          }
        }
      } else {
        console.error('Database error submitting feedback:', error);

        // Check if the error is due to missing table
        if (error.code === 'PGRST106' || error.message?.includes('does not exist')) {
          console.warn('Feedback table does not exist. Please set up the feedback table in admin dashboard.');
        }
      }
    } catch (dbError: any) {
      console.error('Exception submitting feedback to database:', dbError);

      // Check if the error is due to missing table
      if (dbError.code === 'PGRST106' || dbError.message?.includes('does not exist')) {
        console.warn('Feedback table does not exist. Please set up the feedback table in admin dashboard.');
      }
    }

    // Return success if we saved to either local storage or database
    if (savedToDatabase) {
      console.log('✅ Feedback saved to database successfully');
      return { success: true };
    } else if (savedToLocal) {
      console.log('⚠️ Feedback saved to local storage only (database unavailable)');
      return {
        success: true,
        localOnly: true,
        message: 'Feedback saved locally. It will be synced when the database is available.'
      };
    } else {
      throw new Error('Failed to save feedback to both database and local storage');
    }
  } catch (error: any) {
    console.error('Error submitting feedback:', error);
    return {
      success: false,
      error: error.message || 'Failed to submit feedback'
    };
  }
}

/**
 * Generate a UUID for local storage items
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * Get all feedback (admin only)
 * @returns Array of feedback items
 */
export async function getAllFeedback() {
  try {
    try {
      // Try to get feedback from the database
      const { data, error } = await supabase
        .from('feedback')
        .select('*')
        .order('created_at', { ascending: false });

      if (!error) {
        // Get any local feedback and combine with database feedback
        const localFeedback = getLocalFeedback();
        const combinedData = [...(data || []), ...localFeedback];

        // Sort by created_at date, newest first
        combinedData.sort((a, b) => {
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });

        return { success: true, data: combinedData };
      }

      throw error;
    } catch (dbError: any) {
      console.error('Database error fetching feedback:', dbError);

      // Check if the error is due to missing table
      if (dbError.code === 'PGRST106' || dbError.message?.includes('does not exist')) {
        console.warn('Feedback table does not exist. Showing local feedback only.');

        // Fallback to local storage with a specific message
        const localFeedback = getLocalFeedback();
        return {
          success: true,
          data: localFeedback,
          localOnly: true,
          tableNotFound: true,
          message: 'Feedback table not found. Please set up the feedback table in admin dashboard.'
        };
      }

      // Fallback to local storage for other errors
      const localFeedback = getLocalFeedback();
      return {
        success: true,
        data: localFeedback,
        localOnly: true
      };
    }
  } catch (error: any) {
    console.error('Error fetching feedback:', error);
    return {
      success: false,
      error: error.message || 'Failed to fetch feedback',
      data: []
    };
  }
}

/**
 * Get feedback from local storage
 */
function getLocalFeedback() {
  try {
    const localFeedback = localStorage.getItem('pendingFeedback');
    return localFeedback ? JSON.parse(localFeedback) : [];
  } catch (error) {
    console.error('Error reading from local storage:', error);
    return [];
  }
}

/**
 * Update feedback status (admin only)
 * @param id Feedback ID
 * @param status New status ('new', 'read', 'responded', 'archived')
 * @returns Success status and error message if applicable
 */
export async function updateFeedbackStatus(id: string, status: 'new' | 'read' | 'responded' | 'archived') {
  try {
    try {
      // Try to update in the database first
      const { error } = await supabase
        .from('feedback')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (!error) {
        return { success: true };
      }

      throw error;
    } catch (dbError: any) {
      console.error('Database error updating feedback status:', dbError);

      // Try to update in local storage
      const localFeedback = getLocalFeedback();
      const updatedFeedback = localFeedback.map(item => {
        if (item.id === id) {
          return {
            ...item,
            status,
            updated_at: new Date().toISOString()
          };
        }
        return item;
      });

      localStorage.setItem('pendingFeedback', JSON.stringify(updatedFeedback));
      return { success: true, localOnly: true };
    }
  } catch (error: any) {
    console.error('Error updating feedback status:', error);
    return {
      success: false,
      error: error.message || 'Failed to update feedback status'
    };
  }
}

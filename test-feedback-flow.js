import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnZHl5Y2tubHhvaml3aGxxaWNxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyMjkzOTgsImV4cCI6MjA1OTgwNTM5OH0.lWZLRByfsyRqkK7XZfi21qSeEuOZHJKkFJGC_2ojQR8';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testFeedbackFlow() {
  console.log('Testing complete feedback flow...');
  
  try {
    // Step 1: Setup feedback table
    console.log('Step 1: Setting up feedback table...');
    const setupResponse = await fetch('http://localhost:8082/api/setup-feedback-table', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!setupResponse.ok) {
      const errorData = await setupResponse.json();
      console.error('Failed to setup feedback table:', errorData);
      return false;
    }
    
    const setupResult = await setupResponse.json();
    console.log('Feedback table setup result:', setupResult);
    
    // Step 2: Test feedback submission
    console.log('Step 2: Testing feedback submission...');
    const testFeedback = {
      name: 'Test User',
      email: '<EMAIL>',
      subject: 'Test Feedback Submission',
      message: 'This is a test message to verify that the feedback submission flow works correctly from the contact form to the admin dashboard.',
      status: 'new'
    };
    
    const { data: insertData, error: insertError } = await supabase
      .from('feedback')
      .insert(testFeedback)
      .select();
    
    if (insertError) {
      console.error('Error inserting test feedback:', insertError);
      return false;
    }
    
    console.log('Successfully inserted test feedback:', insertData);
    
    // Step 3: Test feedback retrieval
    console.log('Step 3: Testing feedback retrieval...');
    const { data: allFeedback, error: retrieveError } = await supabase
      .from('feedback')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (retrieveError) {
      console.error('Error retrieving feedback:', retrieveError);
      return false;
    }
    
    console.log('Successfully retrieved feedback:', allFeedback);
    console.log(`Total feedback items: ${allFeedback.length}`);
    
    // Step 4: Test feedback status update
    if (insertData && insertData.length > 0) {
      console.log('Step 4: Testing feedback status update...');
      const feedbackId = insertData[0].id;
      
      const { error: updateError } = await supabase
        .from('feedback')
        .update({ status: 'read' })
        .eq('id', feedbackId);
      
      if (updateError) {
        console.error('Error updating feedback status:', updateError);
      } else {
        console.log('Successfully updated feedback status');
      }
    }
    
    console.log('✅ All tests passed! Feedback flow is working correctly.');
    return true;
  } catch (e) {
    console.error('Exception during test:', e);
    return false;
  }
}

testFeedbackFlow();

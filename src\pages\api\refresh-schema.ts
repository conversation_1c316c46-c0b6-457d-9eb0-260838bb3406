import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('Refreshing Supabase schema cache...');

    // If we have the service role key, use it
    if (supabaseServiceKey && supabaseServiceKey !== 'undefined') {
      const supabase = createClient(supabaseUrl, supabaseServiceKey);
      
      // Try to refresh the schema by making a simple query
      const { data, error } = await supabase
        .from('feedback')
        .select('*')
        .limit(1);

      if (error) {
        console.error('Error with service role:', error);
        return res.status(500).json({ 
          error: 'Schema refresh failed',
          details: error.message 
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Schema cache refreshed successfully',
        data: data
      });
    } else {
      // Fallback: provide instructions for manual refresh
      return res.status(200).json({
        success: false,
        message: 'Service role key not available. Please refresh schema manually.',
        instructions: [
          '1. Go to your Supabase dashboard',
          '2. Navigate to the SQL Editor',
          '3. Run: SELECT * FROM feedback LIMIT 1;',
          '4. This will refresh the schema cache'
        ]
      });
    }

  } catch (error) {
    console.error('Error in schema refresh:', error);
    return res.status(500).json({ 
      error: 'Internal server error during schema refresh',
      details: error.message 
    });
  }
}

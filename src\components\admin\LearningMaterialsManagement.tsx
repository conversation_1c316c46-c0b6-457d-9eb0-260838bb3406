import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'react-hot-toast';

// Define interfaces for our data types
interface LearningMaterial {
  id: string;
  title: string;
  summary?: string | null;
  content: string;
  topic_id: string;
  is_premium: boolean;
  order_index: number;
  created_at?: string;
  updated_at?: string;
}

interface Topic {
  id: string;
  title: string;
  description?: string | null;
  icon?: string | null;
}

// Storage keys for form state
const STORAGE_KEY_PREFIX = 'learning_materials_form_';
const STORAGE_KEYS = {
  showForm: `${STORAGE_KEY_PREFIX}show_form`,
  title: `${STORAGE_KEY_PREFIX}title`,
  summary: `${STORAGE_KEY_PREFIX}summary`,
  content: `${STORAGE_KEY_PREFIX}content`,
  topicId: `${STORAGE_KEY_PREFIX}topic_id`,
  isPremium: `${STORAGE_KEY_PREFIX}is_premium`,
  orderIndex: `${STORAGE_KEY_PREFIX}order_index`,
  editingMaterialId: `${STORAGE_KEY_PREFIX}editing_material_id`,
  formHasChanges: `${STORAGE_KEY_PREFIX}has_changes`,
};

export const LearningMaterialsManagement = () => {
  // State variables
  const [materials, setMaterials] = useState<LearningMaterial[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [showForm, setShowForm] = useState(false);
  const [title, setTitle] = useState('');
  const [summary, setSummary] = useState('');
  const [content, setContent] = useState('');
  const [topicId, setTopicId] = useState('');
  const [isPremium, setIsPremium] = useState(false);
  const [orderIndex, setOrderIndex] = useState(0);
  const [editingMaterial, setEditingMaterial] = useState<LearningMaterial | null>(null);
  const [showHtmlWarning, setShowHtmlWarning] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formHasChanges, setFormHasChanges] = useState(false);
  const [showRecoveryNotice, setShowRecoveryNotice] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Helper function to extract body content and clean HTML
  const extractBodyContent = (htmlContent: string | null): string => {
    try {
      if (!htmlContent) return '';

      // If it's a full HTML document, extract just the body content
      if (htmlContent.includes('<!DOCTYPE html>') ||
          htmlContent.includes('<html') ||
          htmlContent.includes('<head>')) {

        // Try to extract content between body tags first
        const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*)<\/body>/i);
        if (bodyMatch && bodyMatch[1]) {
          return bodyMatch[1].trim();
        }

        // If no body tags found, try to remove DOCTYPE, html, head tags
        let cleanedContent = htmlContent;

        // Remove DOCTYPE declaration
        cleanedContent = cleanedContent.replace(/<!DOCTYPE[^>]*>/i, '');

        // Remove html open and close tags
        cleanedContent = cleanedContent.replace(/<html[^>]*>/i, '').replace(/<\/html>/i, '');

        // Remove head section completely
        cleanedContent = cleanedContent.replace(/<head>[\s\S]*?<\/head>/i, '');

        // Remove any remaining body tags
        cleanedContent = cleanedContent.replace(/<body[^>]*>/i, '').replace(/<\/body>/i, '');

        return cleanedContent.trim();
      }

      return htmlContent;
    } catch (e) {
      console.error("Error extracting body content:", e);
      return htmlContent || '';
    }
  };

  // Save form state to localStorage
  const saveFormState = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(STORAGE_KEYS.showForm, JSON.stringify(showForm));
      localStorage.setItem(STORAGE_KEYS.title, title);
      localStorage.setItem(STORAGE_KEYS.summary, summary || '');
      localStorage.setItem(STORAGE_KEYS.content, content);
      localStorage.setItem(STORAGE_KEYS.topicId, topicId);
      localStorage.setItem(STORAGE_KEYS.isPremium, JSON.stringify(isPremium));
      localStorage.setItem(STORAGE_KEYS.orderIndex, JSON.stringify(orderIndex));
      localStorage.setItem(STORAGE_KEYS.editingMaterialId, editingMaterial?.id || '');
      localStorage.setItem(STORAGE_KEYS.formHasChanges, JSON.stringify(formHasChanges));
    } catch (error) {
      console.error('Error saving form state to localStorage:', error);
    }
  }, [showForm, title, summary, content, topicId, isPremium, orderIndex, editingMaterial, formHasChanges]);

  // Load form state from localStorage
  const loadFormState = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      const savedShowForm = localStorage.getItem(STORAGE_KEYS.showForm);
      const savedTitle = localStorage.getItem(STORAGE_KEYS.title);
      const savedSummary = localStorage.getItem(STORAGE_KEYS.summary);
      const savedContent = localStorage.getItem(STORAGE_KEYS.content);
      const savedTopicId = localStorage.getItem(STORAGE_KEYS.topicId);
      const savedIsPremium = localStorage.getItem(STORAGE_KEYS.isPremium);
      const savedOrderIndex = localStorage.getItem(STORAGE_KEYS.orderIndex);
      const savedEditingMaterialId = localStorage.getItem(STORAGE_KEYS.editingMaterialId);
      const savedFormHasChanges = localStorage.getItem(STORAGE_KEYS.formHasChanges);

      if (savedShowForm) setShowForm(JSON.parse(savedShowForm));
      if (savedTitle) setTitle(savedTitle);
      if (savedSummary) setSummary(savedSummary);
      if (savedContent) setContent(savedContent);
      if (savedTopicId) setTopicId(savedTopicId);
      if (savedIsPremium) setIsPremium(JSON.parse(savedIsPremium));
      if (savedOrderIndex) setOrderIndex(JSON.parse(savedOrderIndex));
      if (savedFormHasChanges) setFormHasChanges(JSON.parse(savedFormHasChanges));

      // We'll handle the editingMaterial separately after materials are loaded
      return savedEditingMaterialId;
    } catch (error) {
      console.error('Error loading form state from localStorage:', error);
      return '';
    }
  }, []);

  // Clear form state from localStorage
  const clearFormState = useCallback(() => {
    if (typeof window === 'undefined') return;

    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error('Error clearing form state from localStorage:', error);
    }
  }, []);

  // Load materials and topics
  useEffect(() => {
    fetchMaterials();
    fetchTopics();

    // Load saved form state
    loadFormState();

    // Set up beforeunload event to warn about unsaved changes
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (formHasChanges) {
        const message = 'You have unsaved changes. Are you sure you want to leave?';
        e.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [loadFormState, formHasChanges]);

  const fetchMaterials = async () => {
    try {
      // Use a more explicit type assertion approach
      const response = await supabase
        .from('learning_materials' as any)
        .select('*')
        .order('order_index', { ascending: true });

      if (response.error) throw response.error;

      // First convert to unknown, then to our expected type
      const materialsData = (response.data || []) as unknown as LearningMaterial[];
      setMaterials(materialsData);

      // Only restore from localStorage if we're not currently editing something
      // This prevents localStorage from interfering with the edit process
      if (!editingMaterial) {
        // Check if we need to restore an editing material from localStorage
        const savedEditingMaterialId = localStorage.getItem(STORAGE_KEYS.editingMaterialId);
        const savedFormHasChanges = localStorage.getItem(STORAGE_KEYS.formHasChanges);

        if (savedEditingMaterialId && savedFormHasChanges === 'true') {
          const savedMaterial = materialsData.find(m => m.id === savedEditingMaterialId);
          if (savedMaterial) {
            console.log('Restoring editing state for material:', savedMaterial.id);
            setEditingMaterial(savedMaterial);
            setShowForm(true);
            setShowRecoveryNotice(true);

            // Show a toast notification to inform the user their work was recovered
            toast.success('Your unsaved work has been recovered');

            // Auto-hide the recovery notice after 10 seconds
            setTimeout(() => {
              setShowRecoveryNotice(false);
            }, 10000);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching materials:', error);
      toast.error('Failed to load learning materials');
    }
  };

  const fetchTopics = async () => {
    try {
      const response = await supabase
        .from('topics' as any)
        .select('*')
        .order('title', { ascending: true });

      if (response.error) throw response.error;

      // First convert to unknown, then to our expected type
      const topicsData = (response.data || []) as unknown as Topic[];
      setTopics(topicsData);
    } catch (error) {
      console.error('Error fetching topics:', error);
      toast.error('Failed to load topics');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Process content to ensure we're not saving full HTML documents with DOCTYPE, etc.
      // This ensures we only save the actual content, not the full HTML structure
      const processedContent = extractBodyContent(content);

      const materialData = {
        title,
        summary,
        content: processedContent, // Use the processed content
        topic_id: topicId,
        is_premium: isPremium,
        order_index: orderIndex
      };

      console.log('Submitting form with editingMaterial:', editingMaterial?.id);
      console.log('editingMaterial object:', editingMaterial);
      console.log('Material data:', materialData);

      // More robust check for editing mode
      const isEditing = editingMaterial && editingMaterial.id && typeof editingMaterial.id === 'string' && editingMaterial.id.length > 0;

      if (isEditing) {
        // Update existing material
        console.log('Updating existing material with ID:', editingMaterial.id);
        const { error } = await supabase
          .from('learning_materials' as any)
          .update(materialData)
          .eq('id', editingMaterial.id);

        if (error) {
          console.error('Update error:', error);
          throw error;
        }
        toast.success('Learning material updated successfully');
      } else {
        // Create new material
        console.log('Creating new material - editingMaterial is:', editingMaterial);
        const { error } = await supabase
          .from('learning_materials' as any)
          .insert(materialData);

        if (error) {
          console.error('Insert error:', error);
          throw error;
        }
        toast.success('Learning material created successfully');
      }

      // Reset form, clear localStorage, and refresh materials
      setFormHasChanges(false);
      clearFormState();
      resetForm();
      fetchMaterials();
    } catch (error) {
      console.error('Error saving material:', error);
      toast.error('Failed to save learning material');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (material: LearningMaterial) => {
    console.log('Starting edit for material:', material.id, material.title);

    // Clear any existing localStorage state first to avoid conflicts
    clearFormState();

    // Set editing material first
    setEditingMaterial(material);

    // Set form values
    setTitle(material.title || '');
    setSummary(material.summary || '');
    setTopicId(material.topic_id || '');
    setIsPremium(material.is_premium || false);
    setOrderIndex(material.order_index || 0);

    // Set the content directly - we'll handle HTML in the preview
    setContent(material.content);

    // Show warning if content is complex HTML
    setShowHtmlWarning(
      material.content &&
      (material.content.includes('<!DOCTYPE html>') ||
       material.content.includes('<html') ||
       material.content.includes('<head>'))
    );

    // Show the preview by default when editing
    setShowPreview(true);
    setShowForm(true);

    // Reset form changes state since we're starting fresh
    setFormHasChanges(false);

    console.log('Edit state set, editingMaterial:', material.id);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this learning material?')) {
      return;
    }

    try {
      // Use a type assertion to tell TypeScript that learning_materials is a valid table
      const { error } = await supabase
        .from('learning_materials' as any)
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast.success('Learning material deleted successfully');
      fetchMaterials();
    } catch (error) {
      console.error('Error deleting material:', error);
      toast.error('Failed to delete learning material');
    }
  };

  // Debug: Monitor editingMaterial state changes
  useEffect(() => {
    console.log('editingMaterial state changed:', editingMaterial?.id, editingMaterial?.title);
  }, [editingMaterial]);

  // Track form changes and save to localStorage
  useEffect(() => {
    if (showForm && (title || summary || content || topicId)) {
      setFormHasChanges(true);
      // Only save to localStorage if we're not in editing mode
      // This prevents localStorage from interfering with the editing state
      if (!editingMaterial) {
        saveFormState();
      }
    }
  }, [title, summary, content, topicId, isPremium, orderIndex, showForm, saveFormState, editingMaterial]);

  const resetForm = () => {
    setTitle('');
    setSummary('');
    setContent('');
    setTopicId('');
    setIsPremium(false);
    setOrderIndex(0);
    setEditingMaterial(null);
    setShowHtmlWarning(false);
    setShowForm(false);
    setFormHasChanges(false);

    // Clear saved form state
    clearFormState();
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Learning Materials Management</h1>

      {showRecoveryNotice && (
        <div className="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 flex justify-between items-center">
          <div>
            <p className="font-bold">Your work has been recovered!</p>
            <p>We've restored your unsaved changes from your last session.</p>
          </div>
          <button
            onClick={() => setShowRecoveryNotice(false)}
            className="text-green-700 hover:text-green-900"
          >
            ✕
          </button>
        </div>
      )}

      <button
        onClick={() => {
          if (showForm) {
            // Cancel - reset everything
            resetForm();
          } else {
            // Add new - clear editing state and show form
            clearFormState();
            setEditingMaterial(null);
            setFormHasChanges(false);
            setShowForm(true);
          }
        }}
        className="bg-blue-500 text-white px-4 py-2 rounded mb-4"
      >
        {showForm ? 'Cancel' : 'Add New Material'}
      </button>

      {showForm && (
        <form onSubmit={handleSubmit} className="bg-white p-4 rounded shadow mb-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-semibold">
                {editingMaterial ? 'Edit Learning Material' : 'Add Learning Material'}
              </h2>
              {editingMaterial && (
                <p className="text-sm text-gray-600 mt-1">
                  Editing: {editingMaterial.title} (ID: {editingMaterial.id})
                </p>
              )}
            </div>
            {formHasChanges && (
              <span className="text-amber-600 text-sm font-medium bg-amber-100 px-2 py-1 rounded">
                Unsaved changes
              </span>
            )}
          </div>

          {showHtmlWarning && (
            <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
              <p className="font-medium">HTML Structure Detected</p>
              <p>This content contains a complete HTML document structure (DOCTYPE, html, head tags, etc.).
              The system will automatically extract just the content part when displaying to users.
              Use the preview to see how it will appear.</p>
            </div>
          )}

          <div className="mb-4">
            <label className="block mb-1">Title</label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full p-2 border rounded"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block mb-1">Summary</label>
            <textarea
              value={summary}
              onChange={(e) => setSummary(e.target.value)}
              className="w-full p-2 border rounded"
              rows={3}
            />
          </div>

          <div className="mb-4">
            <label className="block mb-1">Topic</label>
            <select
              value={topicId}
              onChange={(e) => setTopicId(e.target.value)}
              className="w-full p-2 border rounded"
              required
            >
              <option value="">Select a topic</option>
              {topics.map((topic) => (
                <option key={topic.id} value={topic.id}>
                  {topic.title}
                </option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <label className="block mb-1">Content</label>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Paste HTML directly or write content with HTML tags</span>
              <button
                type="button"
                onClick={() => setShowPreview(!showPreview)}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </button>
            </div>
            <textarea
              value={content}
              onChange={(e) => {
                const newContent = e.target.value;
                setContent(newContent);

                // Check if content contains HTML structure and show warning if needed
                setShowHtmlWarning(
                  newContent &&
                  (newContent.includes('<!DOCTYPE html>') ||
                   newContent.includes('<html') ||
                   newContent.includes('<head>'))
                );
              }}
              className="w-full p-2 border rounded font-mono text-sm"
              rows={15}
              placeholder="Enter HTML content here. You can paste complete HTML or just the content part."
            />

            {showPreview && (
              <div className="mt-4">
                <label className="block mb-1">Preview</label>
                <div className="border rounded p-4 min-h-[200px] prose max-w-none">
                  <div
                    dangerouslySetInnerHTML={{
                      __html: showHtmlWarning ? extractBodyContent(content) : content
                    }}
                  />
                  {showHtmlWarning && (
                    <div className="mt-4 text-xs text-gray-500 border-t pt-2">
                      <p>Note: The preview above shows how the content will appear to users (with HTML structure removed).</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="mb-4">
            <label className="block mb-1">Order Index</label>
            <input
              type="number"
              value={orderIndex}
              onChange={(e) => setOrderIndex(parseInt(e.target.value) || 0)}
              className="w-full p-2 border rounded"
            />
          </div>

          <div className="mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isPremium}
                onChange={(e) => setIsPremium(e.target.checked)}
                className="mr-2"
              />
              Premium Content
            </label>
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={resetForm}
              className="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-blue-500 text-white px-4 py-2 rounded"
              disabled={loading}
            >
              {loading ? 'Saving...' : (editingMaterial ? 'Update' : 'Save')}
            </button>
          </div>
        </form>
      )}

      <div className="bg-white rounded shadow">
        <h2 className="text-xl font-semibold p-4 border-b">Learning Materials</h2>

        {materials.length === 0 ? (
          <p className="p-4 text-gray-500">No learning materials found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="bg-gray-100">
                  <th className="p-3 text-left">Title</th>
                  <th className="p-3 text-left">Topic</th>
                  <th className="p-3 text-left">Premium</th>
                  <th className="p-3 text-left">Order</th>
                  <th className="p-3 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {materials.map((material) => (
                  <tr key={material.id} className="border-t">
                    <td className="p-3">{material.title}</td>
                    <td className="p-3">
                      {topics.find(t => t.id === material.topic_id)?.title || 'Unknown'}
                    </td>
                    <td className="p-3">{material.is_premium ? 'Yes' : 'No'}</td>
                    <td className="p-3">{material.order_index}</td>
                    <td className="p-3">
                      <button
                        onClick={() => handleEdit(material)}
                        className="bg-blue-500 text-white px-2 py-1 rounded mr-2"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(material.id)}
                        className="bg-red-500 text-white px-2 py-1 rounded"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};



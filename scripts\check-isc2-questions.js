// <PERSON>ript to check ISC2 Certification quiz questions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkQuestions() {
  try {
    // Find the ISC2 Certification topic
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('id, title')
      .or('title.eq.ISC2 Certification,title.eq.CPN Cybersecurity Certification Exam');
    
    if (topicsError) {
      console.error('Error fetching topics:', topicsError);
      return;
    }
    
    if (!topics || topics.length === 0) {
      console.error('Could not find ISC2 Certification topic');
      return;
    }
    
    console.log(`Found ${topics.length} relevant topics:`);
    topics.forEach(topic => console.log(`- ${topic.title} (${topic.id})`));
    
    // Process each topic
    for (const topic of topics) {
      console.log(`\nChecking questions for topic: ${topic.title}`);
      
      // Get all questions for this topic
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('id, question_text, options, correct_answer, explanation')
        .eq('topic_id', topic.id);
      
      if (questionsError) {
        console.error(`Error fetching questions for topic ${topic.title}:`, questionsError);
        continue;
      }
      
      console.log(`Found ${questions.length} questions for topic ${topic.title}`);
      
      // Display each question
      for (const question of questions) {
        console.log('\n' + '-'.repeat(80));
        console.log(`Question: ${question.question_text}`);
        console.log(`Correct answer: ${question.correct_answer}`);
        
        // Display options
        console.log('\nOptions:');
        for (const [key, value] of Object.entries(question.options)) {
          const isCorrect = key === question.correct_answer ? ' (CORRECT)' : '';
          console.log(`  ${key}: ${value}${isCorrect}`);
        }
        
        console.log(`\nExplanation: ${question.explanation}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
checkQuestions();

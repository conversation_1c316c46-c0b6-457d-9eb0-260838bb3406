import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, UserPlus, Crown, Shield, Trash2, RefreshCw, Database } from "lucide-react";

interface ScalableAdminToolsProps {
  onRefreshUsers: () => void;
}

export function ScalableAdminTools({ onRefreshUsers }: ScalableAdminToolsProps) {
  const [userEmail, setUserEmail] = useState('');
  const [selectedAction, setSelectedAction] = useState('');
  const [selectedPlan, setSelectedPlan] = useState('basic');
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const actions = [
    { value: 'grant_premium', label: 'Grant Premium Access', icon: Crown },
    { value: 'remove_premium', label: 'Remove Premium Access', icon: Crown },
    { value: 'make_admin', label: 'Make Admin', icon: Shield },
    { value: 'remove_admin', label: 'Remove Admin', icon: Shield },
    { value: 'delete_user', label: 'Delete User', icon: Trash2 },
    { value: 'create_profile', label: 'Create Missing Profile', icon: UserPlus },
  ];

  const plans = [
    { value: 'basic', label: 'Basic (1 Month)' },
    { value: 'pro', label: 'Pro (3 Months)' },
    { value: 'elite', label: 'Elite (1 Year)' },
  ];

  const handleExecuteAction = async () => {
    if (!userEmail.trim()) {
      toast({
        title: "Email Required",
        description: "Please enter a user email address",
        variant: "destructive",
      });
      return;
    }

    if (!selectedAction) {
      toast({
        title: "Action Required",
        description: "Please select an action to perform",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);

    try {
      const email = userEmail.trim().toLowerCase();
      
      // First, find the user
      const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        throw new Error(`Failed to fetch users: ${authError.message}`);
      }

      const user = authUsers.users.find(u => u.email?.toLowerCase() === email);
      
      if (!user) {
        throw new Error(`User with email ${email} not found`);
      }

      let result;
      
      switch (selectedAction) {
        case 'grant_premium':
          result = await grantPremiumAccess(user.id, selectedPlan);
          break;
        case 'remove_premium':
          result = await removePremiumAccess(user.id);
          break;
        case 'make_admin':
          result = await makeAdmin(user.id);
          break;
        case 'remove_admin':
          result = await removeAdmin(user.id);
          break;
        case 'delete_user':
          result = await deleteUser(user.id);
          break;
        case 'create_profile':
          result = await createUserProfile(user.id);
          break;
        default:
          throw new Error('Invalid action selected');
      }

      if (result.success) {
        toast({
          title: "Action Completed",
          description: result.message,
        });
        setUserEmail('');
        setSelectedAction('');
        onRefreshUsers();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error executing action:', error);
      toast({
        title: "Action Failed",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const grantPremiumAccess = async (userId: string, planId: string) => {
    try {
      // Calculate expiration date
      const now = new Date();
      let expiresAt = new Date();
      
      switch (planId) {
        case 'basic':
          expiresAt.setMonth(now.getMonth() + 1);
          break;
        case 'pro':
          expiresAt.setMonth(now.getMonth() + 3);
          break;
        case 'elite':
          expiresAt.setFullYear(now.getFullYear() + 1);
          break;
      }

      // Update user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_subscribed: true,
          subscription_expires_at: expiresAt.toISOString(),
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Create subscription record
      const { error: subError } = await supabase
        .from('subscriptions')
        .upsert({
          user_id: userId,
          plan_id: planId,
          amount_paid: 0,
          start_date: now.toISOString(),
          end_date: expiresAt.toISOString(),
          is_active: true,
          last_payment_reference: 'admin_granted',
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (subError) {
        console.warn('Failed to create subscription record:', subError);
      }

      return { 
        success: true, 
        message: `Premium access granted with ${planId} plan until ${expiresAt.toLocaleDateString()}` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const removePremiumAccess = async (userId: string) => {
    try {
      // Update user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          is_subscribed: false,
          subscription_expires_at: null,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Deactivate subscription
      const { error: subError } = await supabase
        .from('subscriptions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (subError) {
        console.warn('Failed to deactivate subscription:', subError);
      }

      return { success: true, message: 'Premium access removed successfully' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const makeAdmin = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_admin: true,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      if (error) {
        throw new Error(`Failed to make user admin: ${error.message}`);
      }

      return { success: true, message: 'User granted admin privileges successfully' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const removeAdmin = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({
          is_admin: false,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (error) {
        throw new Error(`Failed to remove admin privileges: ${error.message}`);
      }

      return { success: true, message: 'Admin privileges removed successfully' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const deleteUser = async (userId: string) => {
    try {
      // This is a dangerous operation - we'll use the RPC function if available
      const { error } = await supabase.rpc('delete_user', { user_id_param: userId });

      if (error) {
        throw new Error(`Failed to delete user: ${error.message}`);
      }

      return { success: true, message: 'User deleted successfully' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const createUserProfile = async (userId: string) => {
    try {
      const { error } = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          is_subscribed: false,
          is_admin: false
        });

      if (error) {
        throw new Error(`Failed to create profile: ${error.message}`);
      }

      return { success: true, message: 'User profile created successfully' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const handleRunMigration = async () => {
    setIsProcessing(true);
    try {
      toast({
        title: "Running Migration",
        description: "Applying database fixes and creating missing profiles...",
      });

      // Run the migration by calling the SQL directly
      const migrationSQL = `
        -- Ensure all users have profiles
        INSERT INTO public.user_profiles (user_id, is_subscribed, is_admin)
        SELECT id, false, false FROM auth.users
        WHERE id NOT IN (SELECT user_id FROM public.user_profiles)
        ON CONFLICT (user_id) DO NOTHING;
      `;

      const { error } = await supabase.rpc('execute_sql', { query: migrationSQL });

      if (error) {
        throw new Error(`Migration failed: ${error.message}`);
      }

      toast({
        title: "Migration Completed",
        description: "Database fixes applied successfully. All users now have profiles.",
      });

      onRefreshUsers();
    } catch (error) {
      console.error('Migration error:', error);
      toast({
        title: "Migration Failed",
        description: error.message || "Failed to run migration",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Admin Tools
        </CardTitle>
        <CardDescription>
          Scalable admin tools for user management. Enter any user email and select an action.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="userEmail">User Email</Label>
            <Input
              id="userEmail"
              type="email"
              placeholder="<EMAIL>"
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              disabled={isProcessing}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="action">Action</Label>
            <Select value={selectedAction} onValueChange={setSelectedAction} disabled={isProcessing}>
              <SelectTrigger>
                <SelectValue placeholder="Select an action" />
              </SelectTrigger>
              <SelectContent>
                {actions.map((action) => {
                  const Icon = action.icon;
                  return (
                    <SelectItem key={action.value} value={action.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {action.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>
        </div>

        {(selectedAction === 'grant_premium') && (
          <div className="space-y-2">
            <Label htmlFor="plan">Subscription Plan</Label>
            <Select value={selectedPlan} onValueChange={setSelectedPlan} disabled={isProcessing}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {plans.map((plan) => (
                  <SelectItem key={plan.value} value={plan.value}>
                    {plan.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div className="flex gap-2">
          <Button 
            onClick={handleExecuteAction} 
            disabled={isProcessing || !userEmail.trim() || !selectedAction}
            className="flex-1"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              'Execute Action'
            )}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleRunMigration}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Fix All Profiles
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

-- Check what columns exist in both profile tables
-- Run this in Supabase SQL Editor to see the actual table structures

-- Check profiles table structure
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'profiles'
ORDER BY ordinal_position;

-- Check user_profiles table structure  
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_profiles'
ORDER BY ordinal_position;

-- Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('profiles', 'user_profiles');

-- Sample data from profiles table
SELECT * FROM public.profiles LIMIT 5;

-- Sample data from user_profiles table  
SELECT * FROM public.user_profiles LIMIT 5;

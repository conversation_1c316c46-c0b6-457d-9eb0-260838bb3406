// This script can be used to import initial quiz data into Supabase
// Run with: node scripts/import-quiz-data.js

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;

// Try to use service role key if available, otherwise fall back to anon key
// For security, the service role key should be used only for scripts, not in the browser
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  console.error('You need to set VITE_SUPABASE_URL and either SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Initial topics data
const topics = [
  {
    title: "CISSP Fundamentals",
    description: "Core concepts and principles of the CISSP certification",
    icon: "shield",
    difficulty: "easy",
    is_active: true
  },
  {
    title: "Cybersecurity Foundation - Hard",
    description: "Check your knowledge level. Attempt the hard level questions in Cybersecurity Foundations.",
    icon: "shieldAlert",
    difficulty: "hard",
    is_active: true
  },
  {
    title: "Cybersecurity Foundation - Medium",
    description: "Check your knowledge level. Try the medium level questions in Cybersecurity Foundations.",
    icon: "shield",
    difficulty: "medium",
    is_active: true
  },
  {
    title: "Cybersecurity Foundation - Easy",
    description: "Get ready to test your knowledge and skills in cybersecurity with our comprehensive quiz app!",
    icon: "shieldCheck",
    difficulty: "easy",
    is_active: true
  },
  {
    title: "Web Security",
    description: "Learn about common web vulnerabilities, OWASP Top 10, and secure coding practices.",
    icon: "code",
    difficulty: "medium",
    is_active: true
  }
];

// Sample questions for Cybersecurity Foundation - Hard
const cyberFoundationHardQuestions = [
  {
    question_text: "Which of the following is a type of social engineering attack?",
    options: {
      "A": "Phishing",
      "B": "Brute force",
      "C": "SQL injection",
      "D": "Denial of Service"
    },
    correct_answer: "A",
    explanation: "Phishing is a social engineering attack where attackers trick users into revealing sensitive information.",
    difficulty: "hard"
  }
  // ...add more questions as needed, using A-D keys...
];

const cyberFoundationMediumQuestions = [
  {
    question_text: "What does the principle of least privilege mean?",
    options: {
      "A": "Users have access to all resources",
      "B": "Users have only the access necessary to perform their job duties",
      "C": "Users can install any software",
      "D": "Users can share passwords"
    },
    correct_answer: "B",
    explanation: "The principle of least privilege restricts users to only the permissions they need.",
    difficulty: "medium"
  }
  // ...add more questions as needed, using A-D keys...
];

const cyberFoundationEasyQuestions = [
  {
    question_text: "What is a strong password?",
    options: {
      "A": "123456",
      "B": "password",
      "C": "A mix of letters, numbers, and symbols",
      "D": "Your name"
    },
    correct_answer: "C",
    explanation: "A strong password uses a mix of letters, numbers, and symbols.",
    difficulty: "easy"
  }
  // ...add more questions as needed, using A-D keys...
];

// Sample questions for Web Security topic
const webSecurityQuestions = [
  {
    question_text: "Which of the following best describes Cross-Site Scripting (XSS)?",
    options: {
      "A": "A vulnerability that allows attackers to inject malicious code into web pages viewed by other users",
      "B": "A vulnerability that allows attackers to execute SQL commands on a database",
      "C": "A vulnerability that allows attackers to access sensitive files on a web server",
      "D": "A vulnerability that allows attackers to intercept communications between a client and server"
    },
    correct_answer: "A",
    explanation: "Cross-Site Scripting (XSS) is a vulnerability that allows attackers to inject client-side scripts into web pages viewed by other users. When successful, XSS allows attackers to bypass same-origin policy, steal cookies, session tokens, or other sensitive information, and potentially perform actions on behalf of the victim.",
    difficulty: "medium"
  },
  {
    question_text: "What is the primary purpose of Content Security Policy (CSP)?",
    options: {
      "A": "To encrypt data transmitted between client and server",
      "B": "To restrict which resources can be loaded by a web page",
      "C": "To authenticate users accessing a web application",
      "D": "To prevent SQL injection attacks"
    },
    correct_answer: "B",
    explanation: "Content Security Policy (CSP) is a security standard that helps prevent XSS and other code injection attacks by restricting which resources (scripts, stylesheets, images, etc.) can be loaded by a web page. It works by specifying which domains are approved sources of content, effectively creating a whitelist of trusted sources.",
    difficulty: "medium"
  },
  {
    question_text: "Which of the following HTTP headers helps prevent clickjacking attacks?",
    options: {
      "A": "Content-Security-Policy",
      "B": "X-Content-Type-Options",
      "C": "X-Frame-Options",
      "D": "Strict-Transport-Security"
    },
    correct_answer: "C",
    explanation: "The X-Frame-Options HTTP header is specifically designed to prevent clickjacking attacks by controlling whether a browser should be allowed to render a page in a <frame>, <iframe>, <embed> or <object>. Valid values are DENY, SAMEORIGIN, or ALLOW-FROM uri, which respectively prevent any framing, allow framing by the same site, or allow framing by the specified URI.",
    difficulty: "medium"
  },
  {
    question_text: "What is the OWASP Top 10?",
    options: {
      "A": "A list of the 10 most common programming languages used in web development",
      "B": "A list of the 10 most critical web application security risks",
      "C": "A list of the 10 most popular web frameworks",
      "D": "A list of the 10 most secure web hosting providers"
    },
    correct_answer: "B",
    explanation: "The OWASP Top 10 is a regularly updated list of the 10 most critical web application security risks, published by the Open Web Application Security Project (OWASP). It represents a broad consensus about the most critical security risks to web applications and is used by many organizations as a standard awareness document for web application security.",
    difficulty: "easy"
  },
  {
    question_text: "Which of the following best describes SQL Injection?",
    options: {
      "A": "A technique to optimize SQL queries for better performance",
      "B": "A method to integrate SQL databases with web applications",
      "C": "A vulnerability where untrusted data is sent to an interpreter as part of a command or query",
      "D": "A tool used by database administrators to manage SQL databases"
    },
    correct_answer: "C",
    explanation: "SQL Injection is a code injection technique where an attacker inserts malicious SQL statements into entry fields in a web application. When successful, this can allow attackers to view, modify, or delete data they normally wouldn't have access to, bypass authentication, or even execute commands on the database server.",
    difficulty: "easy"
  }
];

// Main function to import data
async function importData() {
  try {
    console.log('Starting data import...');

    // Check for existing topics to avoid duplicates
    console.log('Checking for existing topics...');
    const { data: existingTopics, error: existingTopicsError } = await supabase
      .from('topics')
      .select('title, id');

    if (existingTopicsError) {
      throw new Error(`Error checking existing topics: ${existingTopicsError.message}`);
    }

    // Filter out topics that already exist
    const existingTopicTitles = existingTopics.map(topic => topic.title);
    const newTopics = topics.filter(topic => !existingTopicTitles.includes(topic.title));

    let topicsData = existingTopics;

    // Import new topics if any
    if (newTopics.length > 0) {
      console.log(`Importing ${newTopics.length} new topics...`);
      const { data: insertedTopics, error: topicsError } = await supabase
        .from('topics')
        .insert(newTopics)
        .select();

      if (topicsError) {
        throw new Error(`Error importing topics: ${topicsError.message}`);
      }

      console.log(`Successfully imported ${insertedTopics.length} new topics`);

      // Combine existing and newly inserted topics
      topicsData = [...existingTopics, ...insertedTopics];
    } else {
      console.log('All topics already exist in the database. Skipping topic import.');
    }

    // Find Web Security topic ID
    const webSecurityTopic = topicsData.find(topic => topic.title === 'Web Security');

    if (!webSecurityTopic) {
      throw new Error('Web Security topic not found in the database');
    }

    // Check for existing questions to avoid duplicates
    console.log('Checking for existing questions...');
    const { data: existingQuestions, error: existingQuestionsError } = await supabase
      .from('questions')
      .select('question_text, id')
      .eq('topic_id', webSecurityTopic.id);

    if (existingQuestionsError) {
      throw new Error(`Error checking existing questions: ${existingQuestionsError.message}`);
    }

    // Add topic_id to questions
    const questionsWithTopicId = webSecurityQuestions.map(question => ({
      ...question,
      topic_id: webSecurityTopic.id
    }));

    // Filter out questions that already exist
    const existingQuestionTexts = existingQuestions.map(q => q.question_text);
    const newQuestions = questionsWithTopicId.filter(q => !existingQuestionTexts.includes(q.question_text));

    // Import new questions if any
    if (newQuestions.length > 0) {
      console.log(`Importing ${newQuestions.length} new questions...`);
      const { data: questionsData, error: questionsError } = await supabase
        .from('questions')
        .insert(newQuestions)
        .select();

      if (questionsError) {
        throw new Error(`Error importing questions: ${questionsError.message}`);
      }

      console.log(`Successfully imported ${questionsData.length} new questions`);
    } else {
      console.log('All questions already exist in the database. Skipping question import.');
    }

    console.log('Data import completed successfully!');
  } catch (error) {
    console.error('Error during import:', error.message);
    process.exit(1);
  }
}

// Run the import
importData().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});

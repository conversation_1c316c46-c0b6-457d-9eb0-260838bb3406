# GitHub Security Features

## Code Scanning

GitHub's code scanning feature is part of GitHub Advanced Security, which is only available for free on public repositories. For private repositories, it requires a GitHub Enterprise plan or GitHub Advanced Security subscription.

### Current Status

The GitHub workflows in this repository have been modified to avoid Vercel deployment failures related to code scanning:

1. **CodeQL Analysis**: Disabled by default, can be run manually if needed
2. **Dependency Review**: Modified to use npm audit instead of GitHub's dependency review action
3. **Security Scan**: Modified to run only on schedule or manual trigger, not on every push or pull request

### Alternative Security Measures

Instead of GitHub's code scanning, this project uses:

1. **npm audit**: To check for vulnerabilities in dependencies
2. **ESLint with security plugins**: To catch common security issues in JavaScript code
3. **Snyk**: For more comprehensive vulnerability scanning

## How to Run Security Checks Locally

```bash
# Check dependencies for vulnerabilities
npm run security:check

# Run ESLint with security rules
npm run lint

# Run Snyk tests (if configured)
npm run security:check:full
```

## Enabling GitHub Code Scanning

If you upgrade to GitHub Enterprise or make this repository public, you can re-enable the code scanning workflows by editing the workflow files in `.github/workflows/` and uncommenting the trigger events.

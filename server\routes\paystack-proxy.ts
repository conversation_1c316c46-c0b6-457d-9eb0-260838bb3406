import express from 'express';
import fetch from 'node-fetch';

// Generate a unique reference for each transaction
const generateReference = (): string => {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 15);
  return `secquiz-${timestamp}-${randomString}`;
};

const router = express.Router();

// Environment variables
const PAYSTACK_PUBLIC_KEY = process.env.VITE_PAYSTACK_PUBLIC_KEY || '';
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY || '';

// Log keys for debugging (remove in production)
if (!PAYSTACK_PUBLIC_KEY || !PAYSTACK_SECRET_KEY) {
  console.error('Missing Paystack API keys. Please check your environment variables.');
}

// Proxy endpoint for Paystack initialization
router.post('/initialize', async (req, res) => {
  try {
    const { email, amount, currency, metadata, firstname, lastname } = req.body;

    // Generate a reference server-side
    const reference = generateReference();

    // Create the request to Paystack
    // Use the standard transaction/initialize endpoint instead of checkout/request_inline
    const response = await fetch('https://api.paystack.co/transaction/initialize', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        amount,
        currency,
        metadata,
        reference,
        callback_url: `${process.env.VITE_APP_URL || 'https://secquiz.vercel.app'}/payment/success`,
        channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
      }),
    });

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error initializing Paystack payment:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to initialize payment',
      error: error.message
    });
  }
});

// Proxy endpoint for Paystack verification
router.post('/verify', async (req, res) => {
  try {
    const { reference } = req.body;

    // Verify the transaction server-side
    const response = await fetch(`https://api.paystack.co/transaction/verify/${reference}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${PAYSTACK_SECRET_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error('Error verifying Paystack payment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify payment',
      error: error.message
    });
  }
});

export default router;

// Configuration file to centralize access to environment variables

// Supabase Configuration
export const supabaseConfig = {
  url: import.meta.env.VITE_SUPABASE_URL as string,
  anonKey: import.meta.env.VITE_SUPABASE_ANON_KEY as string,
};

// Application Settings
export const appConfig = {
  name: import.meta.env.VITE_APP_NAME as string || 'SecQuiz',
  description: import.meta.env.VITE_APP_DESCRIPTION as string || 'A cybersecurity education platform',
  url: import.meta.env.VITE_APP_URL as string || 'https://secquiz.vercel.app',
};

// Feature Flags
export const featureFlags = {
  enableAdminFeatures: (import.meta.env.VITE_ENABLE_ADMIN_FEATURES as string) === 'true',
  enableAnalytics: (import.meta.env.VITE_ENABLE_ANALYTICS as string) === 'true',
  enableDebugMode: (import.meta.env.VITE_ENABLE_DEBUG_MODE as string) === 'true',
  debug: (import.meta.env.VITE_DEBUG as string) === 'true',
};

// API Configuration
export const apiConfig = {
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT as string, 10) || 30000,
  maxUploadSize: parseInt(import.meta.env.VITE_MAX_UPLOAD_SIZE as string, 10) || 5242880,
};

// Authentication Settings
export const authConfig = {
  redirectUrl: import.meta.env.VITE_AUTH_REDIRECT_URL as string || `${appConfig.url}/auth/verify`,
};

// Helper function to check if we're in development mode
export const isDevelopment = () => import.meta.env.DEV;

// Helper function to check if we're in production mode
export const isProduction = () => import.meta.env.PROD;

// Export a default config object with all settings
export default {
  supabase: supabaseConfig,
  app: appConfig,
  features: featureFlags,
  api: apiConfig,
  auth: authConfig,
  isDevelopment,
  isProduction,
};

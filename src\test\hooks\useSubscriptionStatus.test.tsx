import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useSubscriptionStatus } from '@/hooks/use-subscription-status';
import * as authHelpers from '@/utils/auth-helpers';

// Mock the auth helpers
vi.mock('@/utils/auth-helpers', () => ({
  isUserSubscribed: vi.fn(),
}));

describe('useSubscriptionStatus hook', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  it('returns isSubscribed=false and isLoading=false when user is null', async () => {
    const { result } = renderHook(() => useSubscriptionStatus(null));

    // Skip the initial loading check since it might be too fast
    // and already be false by the time we check

    // Wait for the loading state to change
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // After loading is complete, check the subscription status
    expect(result.current.isSubscribed).toBe(false);
    expect(authHelpers.isUserSubscribed).not.toHaveBeenCalled();
  });

  it('returns isSubscribed=true when user is subscribed', async () => {
    // Mock the isUserSubscribed function to return true
    vi.mocked(authHelpers.isUserSubscribed).mockResolvedValue(true);

    const mockUser = { id: '123', email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useSubscriptionStatus(mockUser));

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the loading state to change
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // After loading is complete, check the subscription status
    expect(result.current.isSubscribed).toBe(true);
    expect(authHelpers.isUserSubscribed).toHaveBeenCalledWith(mockUser);
  });

  it('returns isSubscribed=false when user is not subscribed', async () => {
    // Mock the isUserSubscribed function to return false
    vi.mocked(authHelpers.isUserSubscribed).mockResolvedValue(false);

    const mockUser = { id: '123', email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useSubscriptionStatus(mockUser));

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the loading state to change
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // After loading is complete, check the subscription status
    expect(result.current.isSubscribed).toBe(false);
    expect(authHelpers.isUserSubscribed).toHaveBeenCalledWith(mockUser);
  });

  it('handles errors by setting isSubscribed to false', async () => {
    // Mock the isUserSubscribed function to throw an error
    vi.mocked(authHelpers.isUserSubscribed).mockRejectedValue(new Error('Test error'));

    // Spy on console.error to prevent error output in tests
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const mockUser = { id: '123', email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useSubscriptionStatus(mockUser));

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    // Wait for the loading state to change
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // After loading is complete, check the subscription status
    expect(result.current.isSubscribed).toBe(false);
    expect(authHelpers.isUserSubscribed).toHaveBeenCalledWith(mockUser);
    expect(consoleErrorSpy).toHaveBeenCalled();

    // Restore console.error
    consoleErrorSpy.mockRestore();
  });
});

import { useState, useEffect } from "react";
import { getAllFeedback, updateFeedbackStatus } from "@/services/feedback-service";
import { migrateLocalFeedbackToDatabase } from "@/utils/migrate-local-feedback";
import { createFeedbackTable } from "@/utils/create-feedback-table";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Loader2, Refresh<PERSON>w, Mail, Database, Upload } from "lucide-react";

interface Feedback {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'new' | 'read' | 'responded' | 'archived';
  created_at: string;
  user_id: string | null;
}

export const FeedbackManagement = () => {
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [isCreatingTable, setIsCreatingTable] = useState(false);
  const [hasLocalFeedback, setHasLocalFeedback] = useState(false);
  const { toast } = useToast();

  const loadFeedback = async () => {
    setLoading(true);
    try {
      const result = await getAllFeedback();
      if (result.success) {
        setFeedback(result.data);

        // Check if we have local feedback
        try {
          const localFeedback = localStorage.getItem('pendingFeedback');
          const feedbackArray = localFeedback ? JSON.parse(localFeedback) : [];
          setHasLocalFeedback(feedbackArray.length > 0);
        } catch (e) {
          setHasLocalFeedback(false);
        }

        if (result.tableNotFound) {
          toast({
            title: "Feedback Table Not Found",
            description: "The feedback table doesn't exist. Please set up the feedback table in admin dashboard.",
            variant: "destructive",
          });
        } else if (result.localOnly) {
          toast({
            title: "Local Feedback Only",
            description: "Showing feedback from local storage. Database connection unavailable.",
            variant: "warning",
          });
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to load feedback",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTable = async () => {
    setIsCreatingTable(true);
    try {
      const created = await createFeedbackTable();

      if (created) {
        toast({
          title: "Success",
          description: "Feedback table created successfully",
        });
        loadFeedback();
      } else {
        toast({
          title: "Error",
          description: "Failed to create feedback table",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsCreatingTable(false);
    }
  };

  const handleMigrateLocalFeedback = async () => {
    setIsMigrating(true);
    try {
      const result = await migrateLocalFeedbackToDatabase();

      if (result.success) {
        toast({
          title: "Migration Complete",
          description: result.message,
        });

        if (result.migratedCount > 0) {
          loadFeedback();
        }
      } else {
        toast({
          title: "Migration Failed",
          description: result.error || "Failed to migrate local feedback",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsMigrating(false);
    }
  };

  useEffect(() => {
    loadFeedback();
  }, []);

  const handleStatusChange = async (id: string, status: 'new' | 'read' | 'responded' | 'archived') => {
    setIsUpdating(true);
    try {
      const result = await updateFeedbackStatus(id, status);
      if (result.success) {
        // Update the local state
        setFeedback(prev =>
          prev.map(item =>
            item.id === id ? { ...item, status } : item
          )
        );

        // If we're updating the currently selected feedback
        if (selectedFeedback && selectedFeedback.id === id) {
          setSelectedFeedback({ ...selectedFeedback, status });
        }

        if (result.localOnly) {
          toast({
            title: "Status updated locally",
            description: `Feedback status updated to ${status} in local storage only.`,
          });
        } else {
          toast({
            title: "Status updated",
            description: `Feedback status updated to ${status}`,
          });
        }
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update status",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleViewFeedback = (feedback: Feedback) => {
    // If it's a new feedback, mark it as read
    if (feedback.status === 'new') {
      handleStatusChange(feedback.id, 'read');
    }

    setSelectedFeedback(feedback);
    setIsDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'new':
        return <Badge className="bg-blue-500">New</Badge>;
      case 'read':
        return <Badge className="bg-green-500">Read</Badge>;
      case 'responded':
        return <Badge className="bg-purple-500">Responded</Badge>;
      case 'archived':
        return <Badge variant="outline">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>User Feedback</CardTitle>
            <CardDescription>
              Manage and respond to user feedback and inquiries
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {hasLocalFeedback && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleMigrateLocalFeedback}
                disabled={isMigrating}
                className="flex items-center gap-1"
              >
                {isMigrating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4" />
                )}
                <span>Migrate Local</span>
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={handleCreateTable}
              disabled={isCreatingTable}
              className="flex items-center gap-1"
            >
              {isCreatingTable ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Database className="h-4 w-4" />
              )}
              <span>Create Table</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadFeedback}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : feedback.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No feedback received yet
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {feedback.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell className="whitespace-nowrap">
                      {formatDate(item.created_at)}
                    </TableCell>
                    <TableCell>
                      {item.name}
                    </TableCell>
                    <TableCell>
                      {item.subject}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item.status)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewFeedback(item)}
                        >
                          View
                        </Button>
                        <Select
                          value={item.status}
                          onValueChange={(value) =>
                            handleStatusChange(
                              item.id,
                              value as 'new' | 'read' | 'responded' | 'archived'
                            )
                          }
                          disabled={isUpdating}
                        >
                          <SelectTrigger className="w-[110px]">
                            <SelectValue placeholder="Status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="new">New</SelectItem>
                            <SelectItem value="read">Read</SelectItem>
                            <SelectItem value="responded">Responded</SelectItem>
                            <SelectItem value="archived">Archived</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {/* Feedback Detail Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            {selectedFeedback && (
              <>
                <DialogHeader>
                  <DialogTitle>{selectedFeedback.subject}</DialogTitle>
                  <DialogDescription>
                    From {selectedFeedback.name} ({selectedFeedback.email}) on {formatDate(selectedFeedback.created_at)}
                  </DialogDescription>
                </DialogHeader>

                <div className="mt-4 p-4 bg-muted/50 rounded-md whitespace-pre-wrap">
                  {selectedFeedback.message}
                </div>

                <DialogFooter className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    {getStatusBadge(selectedFeedback.status)}
                    <Select
                      value={selectedFeedback.status}
                      onValueChange={(value) =>
                        handleStatusChange(
                          selectedFeedback.id,
                          value as 'new' | 'read' | 'responded' | 'archived'
                        )
                      }
                      disabled={isUpdating}
                    >
                      <SelectTrigger className="w-[130px]">
                        <SelectValue placeholder="Change Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="read">Read</SelectItem>
                        <SelectItem value="responded">Responded</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Close
                    </Button>
                    <Button
                      onClick={() => {
                        window.open(`mailto:${selectedFeedback.email}?subject=Re: ${selectedFeedback.subject}`, '_blank');
                        handleStatusChange(selectedFeedback.id, 'responded');
                      }}
                    >
                      <Mail className="mr-2 h-4 w-4" /> Reply via Email
                    </Button>
                  </div>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
};

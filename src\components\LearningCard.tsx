import { motion } from "framer-motion";
import { ArrowRight, BookOpen, Shield, Network, Database, Key, Cloud, FileText, ShieldCheck } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

interface LearningCardProps {
  id: string;
  title: string;
  summary: string;
  topicId: string;
  topicTitle?: string;
  isPremium?: boolean;
  icon?: React.ReactNode;
  bgColor?: string;
}

const LearningCard = ({
  id,
  title,
  summary,
  topicId,
  topicTitle,
  isPremium = false,
  icon,
  bgColor,
}: LearningCardProps) => {
  // Function to get background color based on title
  const getBgColorByTitle = (title: string) => {
    const titleLower = title.toLowerCase();
    if (titleLower.includes("cissp")) return "bg-gradient-to-r from-purple-500 to-purple-700";
    if (titleLower.includes("network")) return "bg-gradient-to-r from-blue-500 to-blue-700";
    if (titleLower.includes("web")) return "bg-gradient-to-r from-green-500 to-green-700";
    if (titleLower.includes("cloud")) return "bg-gradient-to-r from-cyan-500 to-cyan-700";
    if (titleLower.includes("crypto")) return "bg-gradient-to-r from-yellow-500 to-yellow-700";
    if (titleLower.includes("ethical") || titleLower.includes("hack")) return "bg-gradient-to-r from-red-500 to-red-700";
    if (titleLower.includes("incident")) return "bg-gradient-to-r from-orange-500 to-orange-700";
    return "bg-gradient-to-r from-indigo-500 to-indigo-700";
  };

  // Function to get icon based on title
  const getIconByTitle = (title: string) => {
    const titleLower = title.toLowerCase();
    if (titleLower.includes("cissp")) return <Shield className="h-8 w-8" />;
    if (titleLower.includes("network")) return <Network className="h-8 w-8" />;
    if (titleLower.includes("web")) return <FileText className="h-8 w-8" />;
    if (titleLower.includes("cloud")) return <Cloud className="h-8 w-8" />;
    if (titleLower.includes("crypto")) return <Key className="h-8 w-8" />;
    if (titleLower.includes("data")) return <Database className="h-8 w-8" />;
    if (titleLower.includes("ethical") || titleLower.includes("hack")) return <ShieldCheck className="h-8 w-8" />;
    return <BookOpen className="h-8 w-8" />;
  };

  return (
    <motion.div
      className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className={`${bgColor || getBgColorByTitle(title)} p-6 relative`}>
        {isPremium && (
          <div className="absolute top-3 right-3 bg-gradient-to-r from-cyber-primary to-cyber-accent text-white text-xs font-bold px-2 py-1 rounded-full">
            PRO
          </div>
        )}
        <div className="flex justify-center items-center h-24">
          {icon || (
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white">
              {getIconByTitle(title)}
            </div>
          )}
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-lg font-bold mb-2 line-clamp-2">{title}</h3>
        
        {topicTitle && (
          <div className="mb-2">
            <span className="text-xs font-medium text-gray-500">
              Topic: {topicTitle}
            </span>
          </div>
        )}
        
        <p className="text-gray-600 text-sm mb-4 line-clamp-3">{summary}</p>
        
        <div className="flex justify-between items-center">
          <Button asChild variant="ghost" size="sm" className="text-cyber-primary hover:text-cyber-primary/90 p-0">
            <Link to={`/learn/${id}`} className="flex items-center">
              <span>Read</span>
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
          
          <Button asChild variant="outline" size="sm" className="text-xs">
            <Link to={`/quiz/${topicId}`}>
              Take Quiz
            </Link>
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default LearningCard;

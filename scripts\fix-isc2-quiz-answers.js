// <PERSON>ript to fix incorrect answers in the ISC2 Certification quiz.
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to get the topic ID for ISC2 Certification
async function getISC2TopicId() {
  const { data, error } = await supabase
    .from('topics')
    .select('id')
    .eq('title', 'ISC2 Certification')
    .single();
  
  if (error) {
    console.error('Error fetching ISC2 Certification topic:', error);
    return null;
  }
  
  return data.id;
}

// Function to get questions for a topic
async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);
  
  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }
  
  return data;
}

// Function to update a question's correct answer
async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

// Specific fixes for questions based on the screenshots
const specificFixes = [
  {
    questionText: "A security engineer discovers that an employee has been accessing sensitive customer data outside of business hours with no clear business justification. Which principle best describes the concern in this scenario?",
    correctAnswer: "1" // "Need to know" (B)
  },
  {
    questionText: "An organization implements multi-factor authentication requiring users to present something they know, something they have, and something they are. What type of authentication model is this?",
    correctAnswer: "1" // "Three-factor authentication" (B)
  },
  {
    questionText: "A security professional is implementing a security control that will remain effective even if attackers gain full knowledge of its design and implementation. Which security principle is being followed?",
    correctAnswer: "2" // "Kerckhoffs' principle" (C)
  }
];

// Main function
async function main() {
  try {
    console.log('Starting to fix ISC2 Certification quiz answers...');
    
    // Get the topic ID for ISC2 Certification
    const topicId = await getISC2TopicId();
    if (!topicId) {
      console.error('Could not find ISC2 Certification topic');
      return;
    }
    
    console.log(`Found ISC2 Certification topic with ID: ${topicId}`);
    
    // Get all questions for this topic
    const questions = await getQuestionsForTopic(topicId);
    console.log(`Found ${questions.length} questions for ISC2 Certification`);
    
    // Track questions that need fixing
    const questionsToFix = [];
    
    // Check each question against our specific fixes
    for (const question of questions) {
      for (const fix of specificFixes) {
        if (question.question_text.includes(fix.questionText) || 
            fix.questionText.includes(question.question_text)) {
          
          // If the correct answer is already set correctly, skip
          if (question.correct_answer === fix.correctAnswer) {
            console.log(`Question "${question.question_text.substring(0, 50)}..." already has the correct answer.`);
            continue;
          }
          
          questionsToFix.push({
            id: question.id,
            text: question.question_text,
            currentAnswer: question.correct_answer,
            newAnswer: fix.correctAnswer
          });
          
          break;
        }
      }
    }
    
    console.log(`Found ${questionsToFix.length} questions that need fixing.`);
    
    // Display questions that will be fixed
    for (const q of questionsToFix) {
      console.log(`\nQuestion: ${q.text.substring(0, 100)}...`);
      console.log(`Current answer: ${q.currentAnswer}`);
      console.log(`New answer: ${q.newAnswer}`);
    }
    
    // Ask for confirmation before updating
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    readline.question(`\nDo you want to update these ${questionsToFix.length} questions? (y/n): `, async (answer) => {
      if (answer.toLowerCase() === 'y') {
        console.log('Updating questions...');
        
        let successCount = 0;
        
        for (const q of questionsToFix) {
          const success = await updateQuestionCorrectAnswer(q.id, q.newAnswer);
          if (success) {
            successCount++;
            console.log(`Updated question: ${q.id}`);
          }
        }
        
        console.log(`\nUpdate complete. ${successCount}/${questionsToFix.length} questions updated successfully.`);
      } else {
        console.log('Update cancelled.');
      }
      
      readline.close();
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the main function
main();

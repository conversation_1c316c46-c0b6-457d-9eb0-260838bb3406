
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";

export interface User {
  id: string;
  email: string;
  full_name?: string;
  created_at: string;
  last_sign_in_at: string | null;
  is_subscribed: boolean;
  is_admin: boolean;
  subscription_expires_at: string | null;
  subscription_status?: 'active' | 'expired' | 'free';
  subscription_plan?: string;
}

export function useAdminUsers() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Enhanced admin checking and fallback logic
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      // First, check if current user is admin
      let isCurrentUserAdmin = false;
      try {
        const { data: isAdminResult, error: adminCheckError } = await supabase.rpc('is_admin');
        if (!adminCheckError) {
          isCurrentUserAdmin = isAdminResult;
        }
      } catch (adminError) {
        console.warn('Admin check failed:', adminError);
      }

      // Try to use the new consolidated function first (only if user is admin)
      if (isCurrentUserAdmin) {
        try {
          const { data: profilesData, error: profilesError } = await supabase.rpc('get_all_user_profiles');

          if (!profilesError && profilesData) {
            const combinedUsers: User[] = profilesData.map((profile) => {
              // Determine subscription status
              let subscriptionStatus: 'active' | 'expired' | 'free' = 'free';
              let isSubscribed = false;

              if (profile.is_subscribed && profile.subscription_expires_at) {
                const expiresAt = new Date(profile.subscription_expires_at);
                const now = new Date();

                if (expiresAt > now) {
                  subscriptionStatus = 'active';
                  isSubscribed = true;
                } else {
                  subscriptionStatus = 'expired';
                  isSubscribed = false;
                }
              }

              return {
                id: profile.user_id,
                email: profile.email || '',
                full_name: profile.full_name,
                created_at: profile.created_at,
                last_sign_in_at: profile.last_sign_in_at,
                is_subscribed: isSubscribed,
                is_admin: profile.is_admin || false,
                subscription_expires_at: profile.subscription_expires_at || null,
                subscription_status: subscriptionStatus,
                subscription_plan: profile.subscription_plan
              };
            });

            setUsers(combinedUsers);
            return;
          }
        } catch (rpcError) {
          console.warn('RPC function failed, falling back to manual fetch:', rpcError);
        }
      }

      // Fallback approach: Try to get user profiles directly (with proper error handling)
      try {
        // First try to get user profiles data
        const { data: profilesData, error: profilesError } = await supabase
          .from('user_profiles')
          .select('*');

        if (!profilesError && profilesData) {
          // If we can access user_profiles, use that data
          const combinedUsers: User[] = profilesData.map((profile) => {
            // Determine subscription status
            let subscriptionStatus: 'active' | 'expired' | 'free' = 'free';
            let isSubscribed = false;

            if (profile.is_subscribed && profile.subscription_expires_at) {
              const expiresAt = new Date(profile.subscription_expires_at);
              const now = new Date();

              if (expiresAt > now) {
                subscriptionStatus = 'active';
                isSubscribed = true;
              } else {
                subscriptionStatus = 'expired';
                isSubscribed = false;
              }
            }

            return {
              id: profile.user_id,
              email: profile.email || '',
              full_name: profile.full_name,
              created_at: profile.created_at,
              last_sign_in_at: profile.last_sign_in_at,
              is_subscribed: isSubscribed,
              is_admin: profile.is_admin || false,
              subscription_expires_at: profile.subscription_expires_at || null,
              subscription_status: subscriptionStatus,
              subscription_plan: profile.subscription_plan
            };
          });

          setUsers(combinedUsers);
          return;
        }
      } catch (profilesError) {
        console.warn('Error accessing user_profiles directly:', profilesError);
      }

      // Last resort: Try auth.admin.listUsers (only if user is admin)
      if (isCurrentUserAdmin) {
        try {
          const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();

          if (authError) {
            throw new Error(`Auth admin access failed: ${authError.message}`);
          }

          // Get user profiles data
          const { data: profilesData, error: profilesError } = await supabase
            .from('user_profiles')
            .select('*');

          if (profilesError) {
            console.warn('Error fetching user profiles:', profilesError);
          }

          // Combine the data
          const combinedUsers: User[] = authUsers.users.map((authUser) => {
            const profile = profilesData?.find((p) => p.user_id === authUser.id);

            // Determine subscription status
            let subscriptionStatus: 'active' | 'expired' | 'free' = 'free';
            let isSubscribed = false;

            if (profile?.is_subscribed && profile?.subscription_expires_at) {
              const expiresAt = new Date(profile.subscription_expires_at);
              const now = new Date();

              if (expiresAt > now) {
                subscriptionStatus = 'active';
                isSubscribed = true;
              } else {
                subscriptionStatus = 'expired';
                isSubscribed = false;
              }
            }

            return {
              id: authUser.id,
              email: profile?.email || authUser.email || '',
              full_name: profile?.full_name || authUser.user_metadata?.full_name,
              created_at: authUser.created_at,
              last_sign_in_at: authUser.last_sign_in_at,
              is_subscribed: isSubscribed,
              is_admin: profile?.is_admin || false,
              subscription_expires_at: profile?.subscription_expires_at || null,
              subscription_status: subscriptionStatus,
              subscription_plan: profile?.subscription_plan
            };
          });

          setUsers(combinedUsers);
          return;
        } catch (authAdminError) {
          console.error('Auth admin listUsers failed:', authAdminError);
        }
      }

      // If we reach here, user is not admin or all methods failed
      if (!isCurrentUserAdmin) {
        setError("Admin privileges required to view users. Please contact an administrator to grant you admin access.");
      } else {
        setError("Failed to fetch users. Please check your permissions and try again.");
      }
      setUsers([]);
    } catch (error) {
      console.error("Error fetching users:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch users");
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // Update user subscription status
  const updateUserSubscription = async (userId: string, isSubscribed: boolean, expiresAt?: string | null, planId: string = 'pro') => {
    try {
      console.log(`Admin updating subscription for user ${userId}: isSubscribed=${isSubscribed}, planId=${planId}, expiresAt=${expiresAt}`);

      // Update the users array in memory to ensure UI reflects changes
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId
            ? { ...user, is_subscribed: isSubscribed, subscription_expires_at: expiresAt }
            : user
        )
      );

      // Calculate proper expiration date if not provided
      let finalExpiresAt = expiresAt;
      if (isSubscribed && !finalExpiresAt) {
        const daysToAdd = planId === 'elite' ? 365 : 7; // Elite: 1 year, Basic/Pro: 1 week
        finalExpiresAt = new Date(Date.now() + daysToAdd * 24 * 60 * 60 * 1000).toISOString();
        console.log(`Calculated expiration date: ${finalExpiresAt}`);
      }

      // Use upsert to handle both insert and update cases, which is more reliable
      console.log('Upserting user profile...');
      const profileResult = await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_subscribed: isSubscribed,
          subscription_expires_at: finalExpiresAt
        }, {
          onConflict: 'user_id'
        });

      if (profileResult.error) {
        console.error('Error upserting user profile:', profileResult.error);

        // If upsert fails due to RLS, try a different approach
        if (profileResult.error.message.includes('policy') || profileResult.error.message.includes('permission')) {
          console.log('RLS policy issue detected, trying alternative approach...');

          // Try to update first
          const updateResult = await supabase
            .from('user_profiles')
            .update({
              is_subscribed: isSubscribed,
              subscription_expires_at: finalExpiresAt
            })
            .eq('user_id', userId);

          if (updateResult.error) {
            console.error('Update also failed:', updateResult.error);
            throw new Error(`Failed to update user profile: ${updateResult.error.message}`);
          }

          console.log('Profile updated successfully via update operation');
        } else {
          throw new Error(`Failed to update user profile: ${profileResult.error.message}`);
        }
      } else {
        console.log('Profile upserted successfully');
      }

      console.log('User profile updated successfully');

      // Handle subscription record
      if (isSubscribed) {
        // Check if subscription exists
        const { data: existingSubscription, error: subCheckError } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

        if (subCheckError) {
          console.error('Error checking existing subscription:', subCheckError);
        }

        const startDate = new Date().toISOString();
        const endDate = finalExpiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();

        let subscriptionResult;
        if (existingSubscription) {
          console.log('Updating existing subscription');
          // Update existing subscription
          subscriptionResult = await supabase
            .from('subscriptions')
            .update({
              plan_id: planId,
              start_date: startDate,
              end_date: endDate,
              is_active: true,
              last_payment_reference: 'admin_granted',
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);
        } else {
          console.log('Creating new subscription');
          // Create new subscription
          subscriptionResult = await supabase
            .from('subscriptions')
            .insert({
              user_id: userId,
              plan_id: planId,
              amount_paid: 0, // Admin-granted subscription
              start_date: startDate,
              end_date: endDate,
              is_active: true,
              last_payment_reference: 'admin_granted',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });
        }

        if (subscriptionResult.error) {
          console.error('Error updating subscription:', subscriptionResult.error);
          // Don't throw error here, profile update was successful
        } else {
          console.log('Subscription record updated successfully');
        }
      } else {
        console.log('Deactivating subscription');
        // Deactivate subscription
        const { error: deactivateError } = await supabase
          .from('subscriptions')
          .update({
            is_active: false,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        if (deactivateError) {
          console.error('Error deactivating subscription:', deactivateError);
        } else {
          console.log('Subscription deactivated successfully');
        }
      }

      // Refresh the users list to get the latest data
      console.log('Refreshing users list...');
      await fetchUsers();

      console.log('Admin subscription update completed successfully');
      return { success: true };
    } catch (error) {
      console.error("Error updating user subscription:", error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  };

  // Update user admin status
  const updateUserAdminStatus = async (userId: string, isAdmin: boolean) => {
    try {
      // Update the users array in memory
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId
            ? { ...user, is_admin: isAdmin }
            : user
        )
      );

      // Execute SQL to update the database (using the available RPC function)
      try {
        // Use the is_admin RPC function to check if the current user is an admin
        const { data: isCurrentUserAdmin } = await supabase.rpc('is_admin');

        if (!isCurrentUserAdmin) {
          throw new Error("Only admins can change admin status");
        }

        // Use the execute_sql RPC function if available
        const sql = `
          DO $$
          BEGIN
            -- Handle admin_users table
            IF ${isAdmin} THEN
              -- Insert into admin_users if not exists
              INSERT INTO admin_users (user_id, is_admin)
              VALUES ('${userId}', true)
              ON CONFLICT (user_id) DO NOTHING;
            ELSE
              -- Remove from admin_users
              DELETE FROM admin_users
              WHERE user_id = '${userId}';
            END IF;

            -- Update profiles table
            UPDATE profiles
            SET is_admin = ${isAdmin}, updated_at = NOW()
            WHERE id = '${userId}';

            -- Also update user_profiles table if it exists
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_profiles') THEN
              -- We don't have is_admin in user_profiles based on the schema
              -- But we'll update the record to keep timestamps current
              UPDATE user_profiles
              SET updated_at = NOW()
              WHERE user_id = '${userId}';
            END IF;
          END $$;
        `;

        await supabase.rpc('execute_sql', { query: sql });
      } catch (sqlError) {
        console.error("Error executing SQL:", sqlError);
        // Continue anyway, we've already updated the UI
      }

      // Refresh the users list
      await fetchUsers();

      return { success: true };
    } catch (error) {
      console.error("Error updating user admin status:", error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  };

  // Delete a user (this is a sensitive operation and might require additional permissions)
  const deleteUser = async (userId: string) => {
    try {
      // Check if current user is admin
      const { data: isAdmin, error: adminCheckError } = await supabase.rpc('is_admin');

      if (adminCheckError) {
        console.error("Error checking admin status:", adminCheckError);
        throw new Error("Could not verify admin privileges: " + adminCheckError.message);
      }

      if (!isAdmin) {
        throw new Error("Only admins can delete users");
      }

      // First try to use the delete_user RPC function
      try {
        console.log("Attempting to delete user with ID:", userId);
        const { error: deleteError } = await supabase.rpc('delete_user', { user_id: userId });

        if (deleteError) {
          console.error("Error using delete_user RPC function:", deleteError);
          throw new Error("Failed to delete user: " + deleteError.message);
        }
      } catch (rpcError) {
        console.error("RPC delete_user function failed:", rpcError);

        // Fallback to manual deletion if RPC fails
        try {
          console.log("Falling back to manual deletion for user:", userId);

          // First clean up related records
          const cleanupPromises = [
            supabase.from('profiles').delete().eq('id', userId),
            supabase.from('user_profiles').delete().eq('user_id', userId),
            supabase.from('subscriptions').delete().eq('user_id', userId),
            supabase.from('payments').delete().eq('user_id', userId),
            supabase.from('quiz_attempts').delete().eq('user_id', userId),
            supabase.from('feedback').delete().eq('user_id', userId)
          ];

          // Execute all cleanup operations
          await Promise.all(cleanupPromises);

          // Finally, try to delete the user from auth.users using admin API
          const { error: authDeleteError } = await supabase.auth.admin.deleteUser(userId);

          if (authDeleteError) {
            console.error("Error deleting user from auth.users:", authDeleteError);
            throw new Error("Failed to delete user from authentication system: " + authDeleteError.message);
          }
        } catch (manualDeleteError) {
          console.error("Manual deletion failed:", manualDeleteError);
          throw new Error("User deletion failed after multiple attempts. Please try the 'Fix Delete User Function' button and try again.");
        }
      }

      // Refresh the users list
      await fetchUsers();

      return { success: true };
    } catch (error) {
      console.error("Error deleting user:", error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
  };

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  return {
    users,
    setUsers,
    loading,
    error,
    fetchUsers,
    updateUserSubscription,
    updateUserAdminStatus,
    deleteUser
  };
}

// <PERSON>ript to fix all incorrect answers in the ISC2 Certification quiz
// This script analyzes the explanation text to determine the correct answer
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to get the topic ID for ISC2 Certification
async function getISC2TopicId() {
  const { data, error } = await supabase
    .from('topics')
    .select('id')
    .eq('title', 'ISC2 Certification')
    .single();
  
  if (error) {
    // Try with CPN Cybersecurity Certification Exam as an alternative
    const { data: cpnData, error: cpnError } = await supabase
      .from('topics')
      .select('id')
      .eq('title', 'CPN Cybersecurity Certification Exam')
      .single();
    
    if (cpnError) {
      console.error('Error fetching certification topic:', cpnError);
      return null;
    }
    
    return cpnData.id;
  }
  
  return data.id;
}

// Function to get questions for a topic
async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);
  
  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }
  
  return data;
}

// Function to update a question's correct answer
async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

// Specific fixes for questions based on the screenshots
const specificFixes = {
  "A security engineer discovers that an employee has been accessing sensitive customer data outside of business hours with no clear business justification": "1", // "Need to know" (B)
  "An organization implements multi-factor authentication requiring users to present something they know, something they have, and something they are": "1", // "Three-factor authentication" (B)
  "A security professional is implementing a security control that will remain effective even if attackers gain full knowledge of its design and implementation": "2", // "Kerckhoffs' principle" (C)
};

// Function to analyze a question and determine the correct answer based on the explanation
function analyzeQuestion(question) {
  const { id, question_text, options, correct_answer, explanation } = question;
  
  // Check if this is a question with a specific fix
  for (const [partialText, correctAnswer] of Object.entries(specificFixes)) {
    if (question_text.includes(partialText)) {
      return {
        id,
        question_text,
        current_answer: correct_answer,
        suggested_answer: correctAnswer,
        confidence: 'high',
        reason: 'Specific fix from known issues'
      };
    }
  }
  
  // If no specific fix, analyze the explanation
  const explanationLower = explanation ? explanation.toLowerCase() : '';
  const optionKeys = Object.keys(options);
  
  // Initialize result
  const result = {
    id,
    question_text,
    current_answer: correct_answer,
    suggested_answer: null,
    confidence: 'low',
    reason: ''
  };
  
  // Look for key phrases in the explanation that indicate the correct answer
  let bestMatchKey = null;
  let bestMatchScore = 0;
  let bestMatchReason = '';
  
  for (const key of optionKeys) {
    const optionText = options[key].toLowerCase();
    let score = 0;
    let reason = '';
    
    // Check if the option text is mentioned in the explanation
    if (explanationLower.includes(optionText)) {
      score += 2;
      reason += `Option text "${optionText}" found in explanation. `;
    }
    
    // Check for phrases that indicate this is the correct answer
    const correctPhrases = [
      'correct answer',
      'right answer',
      'this is the answer',
      'this option is correct',
      'this is correct',
      'is correct because',
      'is the correct',
      'is the right',
      'is the proper',
      'is the appropriate'
    ];
    
    for (const phrase of correctPhrases) {
      if (explanationLower.includes(`${optionText} ${phrase}`) || 
          explanationLower.includes(`${phrase} ${optionText}`)) {
        score += 3;
        reason += `Found phrase indicating this is correct: "${phrase}". `;
      }
    }
    
    // Check for phrases that indicate other options are incorrect
    const incorrectPhrases = [
      'incorrect',
      'wrong',
      'not the answer',
      'not correct',
      'not appropriate',
      'not proper'
    ];
    
    for (const key2 of optionKeys) {
      if (key2 === key) continue;
      
      const otherOptionText = options[key2].toLowerCase();
      for (const phrase of incorrectPhrases) {
        if (explanationLower.includes(`${otherOptionText} ${phrase}`) || 
            explanationLower.includes(`${phrase} ${otherOptionText}`)) {
          score += 1;
          reason += `Found phrase indicating other option "${otherOptionText}" is incorrect. `;
        }
      }
    }
    
    // Update best match if this option has a higher score
    if (score > bestMatchScore) {
      bestMatchScore = score;
      bestMatchKey = key;
      bestMatchReason = reason;
    }
  }
  
  // Determine confidence level based on match score
  if (bestMatchScore > 4) {
    result.confidence = 'high';
  } else if (bestMatchScore > 2) {
    result.confidence = 'medium';
  }
  
  // If we found a match and it's different from the current answer
  if (bestMatchKey && bestMatchKey !== correct_answer) {
    result.suggested_answer = bestMatchKey;
    result.reason = bestMatchReason;
  } else if (bestMatchKey && bestMatchKey === correct_answer) {
    // The current answer is already correct
    result.suggested_answer = correct_answer;
    result.reason = 'Current answer matches analysis';
  } else {
    // No clear match found
    result.reason = 'No clear match found in explanation';
  }
  
  return result;
}

// Main function
async function main() {
  try {
    console.log('Starting to analyze and fix ISC2 Certification quiz answers...');
    
    // Get the topic ID for ISC2 Certification
    const topicId = await getISC2TopicId();
    if (!topicId) {
      console.error('Could not find ISC2 Certification topic');
      return;
    }
    
    console.log(`Found certification topic with ID: ${topicId}`);
    
    // Get all questions for this topic
    const questions = await getQuestionsForTopic(topicId);
    console.log(`Found ${questions.length} questions for the certification quiz`);
    
    // Analyze each question
    const analysisResults = questions.map(analyzeQuestion);
    
    // Filter questions that need fixing with medium or high confidence
    const questionsToFix = analysisResults.filter(result => 
      result.suggested_answer && 
      result.suggested_answer !== result.current_answer &&
      (result.confidence === 'high' || result.confidence === 'medium')
    );
    
    console.log(`Found ${questionsToFix.length} questions that need fixing with medium or high confidence.`);
    
    // Display questions that will be fixed
    for (const q of questionsToFix) {
      console.log(`\nQuestion: ${q.question_text.substring(0, 100)}...`);
      console.log(`Current answer: ${q.current_answer}`);
      console.log(`Suggested answer: ${q.suggested_answer}`);
      console.log(`Confidence: ${q.confidence}`);
      console.log(`Reason: ${q.reason}`);
    }
    
    // Ask for confirmation before updating
    const readline = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    readline.question(`\nDo you want to update these ${questionsToFix.length} questions? (y/n): `, async (answer) => {
      if (answer.toLowerCase() === 'y') {
        console.log('Updating questions...');
        
        let successCount = 0;
        
        for (const q of questionsToFix) {
          const success = await updateQuestionCorrectAnswer(q.id, q.suggested_answer);
          if (success) {
            successCount++;
            console.log(`Updated question: ${q.id}`);
          }
        }
        
        console.log(`\nUpdate complete. ${successCount}/${questionsToFix.length} questions updated successfully.`);
      } else {
        console.log('Update cancelled.');
      }
      
      readline.close();
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the main function
main();

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file in the parent directory
const envPath = join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error('.env file not found at:', envPath);
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkLearningMaterials() {
  try {
    console.log('Checking learning materials...');

    // Query all learning materials
    const { data, error } = await supabase
      .from('learning_materials')
      .select(`
        *,
        topics:topic_id (
          title,
          description,
          icon
        )
      `)
      .order('title');

    if (error) {
      console.error('Error querying learning materials:', error);
      return;
    }

    console.log('Found', data.length, 'learning materials');

    // Check each material for potential issues
    data.forEach((material, index) => {
      console.log(`\n[${index + 1}] Material: ${material.title}`);
      console.log(`  ID: ${material.id}`);
      console.log(`  Topic: ${material.topics?.title || 'Unknown'}`);
      console.log(`  Topic ID: ${material.topic_id}`);
      console.log(`  Is Premium: ${material.is_premium}`);

      // Check content
      if (!material.content) {
        console.log('  Content: MISSING');
      } else {
        const contentLength = material.content.length;
        console.log(`  Content length: ${contentLength} characters`);

        // Check if content is HTML
        const isHTML = material.content.includes('<') && material.content.includes('>');
        console.log(`  Content format: ${isHTML ? 'HTML' : 'Plain text'}`);

        // Check for potential issues
        if (contentLength > 50000) {
          console.log('  WARNING: Content is very large (>50KB)');
        }

        if (material.content.includes('<!DOCTYPE html>')) {
          console.log('  WARNING: Content contains full HTML document with DOCTYPE');
        }

        // Check for script tags
        if (material.content.includes('<script')) {
          console.log('  WARNING: Content contains script tags');
        }

        // Check for iframe tags
        if (material.content.includes('<iframe')) {
          console.log('  WARNING: Content contains iframe tags');
        }

        // Check for null or undefined topic_id
        if (!material.topic_id) {
          console.log('  WARNING: Missing topic_id');
        }

        // Check for missing topic reference
        if (!material.topics) {
          console.log('  WARNING: Topic reference is missing');
        }
      }

      // Check summary
      if (!material.summary) {
        console.log('  Summary: MISSING');
      } else {
        console.log(`  Summary length: ${material.summary.length} characters`);
      }
    });

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
checkLearningMaterials();

import express from 'express';
import { checkAndUpdateExpiredSubscriptions } from '../services/subscription';

const router = express.Router();

// Endpoint to check for expired subscriptions
router.post('/check-expired', async (req, res) => {
  try {
    const result = await checkAndUpdateExpiredSubscriptions();
    
    if (result.success) {
      return res.status(200).json({
        success: true,
        message: `Successfully checked for expired subscriptions. Updated ${result.updated} of ${result.total} subscriptions.`,
        updated: result.updated,
        total: result.total
      });
    } else {
      return res.status(500).json({
        success: false,
        message: 'Failed to check for expired subscriptions',
        error: result.error
      });
    }
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during subscription check',
      error: error.message
    });
  }
});

export default router;

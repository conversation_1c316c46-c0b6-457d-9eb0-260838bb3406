import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import Navbar from '@/components/Navbar';
import * as useAuthModule from '@/hooks/use-auth';
import * as useAdminStatusModule from '@/hooks/use-admin-status';
import * as useMobileModule from '@/hooks/use-mobile';

// Mock the hooks
vi.mock('@/hooks/use-auth', async () => {
  const actual = await vi.importActual('@/hooks/use-auth');
  return {
    ...actual,
    useAuth: vi.fn(),
  };
});

vi.mock('@/hooks/use-admin-status', async () => {
  const actual = await vi.importActual('@/hooks/use-admin-status');
  return {
    ...actual,
    useAdminStatus: vi.fn(),
  };
});

vi.mock('@/hooks/use-mobile', async () => {
  const actual = await vi.importActual('@/hooks/use-mobile');
  return {
    ...actual,
    useIsMobile: vi.fn(),
  };
});

// Create a mock Navbar component for testing
vi.mock('@/components/Navbar', () => ({
  default: () => {
    const { useAuth } = useAuthModule;
    const { useAdminStatus } = useAdminStatusModule;
    const { useIsMobile } = useMobileModule;

    const isMobile = useIsMobile();
    const { user } = useAuth();
    const { isAdmin } = useAdminStatus(user);

    if (isMobile) {
      return (
        <div data-testid="mobile-navbar">
          <a href="/about">About</a>
          <a href="/contact">Contact</a>
          {user && <a href="/profile" data-testid="profile-icon">Profile Icon</a>}
        </div>
      );
    }

    return (
      <div data-testid="desktop-navbar">
        <nav>
          <a href="/">Home</a>
          <a href="/quizzes">Quizzes</a>
          <a href="/about">About</a>
          <a href="/contact">Contact</a>
          {isAdmin && <a href="/admin">Admin</a>}
        </nav>
        <div>
          {user ? (
            <a href="/profile">My Profile</a>
          ) : (
            <a href="/auth">Sign In</a>
          )}
        </div>
      </div>
    );
  },
}));

describe('Navbar component', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: null,
      session: null,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    vi.mocked(useAdminStatusModule.useAdminStatus).mockReturnValue({
      isAdmin: false,
      isLoading: false,
    });

    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(false);
  });

  it('renders desktop navbar when not on mobile', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(false);

    render(<Navbar />);

    // Check for desktop navigation elements
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Quizzes')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();
    expect(screen.getByText('Sign In')).toBeInTheDocument();
  });

  it('renders mobile navbar when on mobile', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);

    render(<Navbar />);

    // Check for mobile navigation elements
    expect(screen.getByText('About')).toBeInTheDocument();
    expect(screen.getByText('Contact')).toBeInTheDocument();

    // Mobile navbar doesn't show all links
    expect(screen.queryByText('Home')).not.toBeInTheDocument();
    expect(screen.queryByText('Quizzes')).not.toBeInTheDocument();
  });

  it('shows profile button when user is logged in on mobile', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: { id: '123', email: '<EMAIL>' } as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    render(<Navbar />);

    // Should show profile icon
    expect(screen.getByTestId('profile-icon')).toBeInTheDocument();
  });

  it('shows "My Profile" button when user is logged in on desktop', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(false);
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: { id: '123', email: '<EMAIL>' } as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    render(<Navbar />);

    // Should show My Profile button
    expect(screen.getByText('My Profile')).toBeInTheDocument();
  });

  it('shows "Sign In" button when user is not logged in on desktop', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(false);
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: null,
      session: null,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    render(<Navbar />);

    // Should show Sign In button
    expect(screen.getByText('Sign In')).toBeInTheDocument();
  });
});

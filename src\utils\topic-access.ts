import { User } from "@supabase/supabase-js";
import { isUserSubscribed } from "./auth-helpers";
import { supabase } from "@/integrations/supabase/client";

// Topic titles that should be accessible to everyone (even unauthenticated users)
export const PUBLIC_TOPICS = ["CISSP Fundamentals"];

// Topic titles that should be accessible to authenticated users (but not premium)
// These topics and all their related materials are free for registered users with unlimited access
export const AUTHENTICATED_TOPICS = [
  
  "CIA Triad: Confidentiality, Integrity, and Availability",
  "ISC2 Certification",
  "Cybersecurity Awareness Skill"
];

// Hardcoded list of premium users for synchronous checks
// This list should include all emails that have active subscriptions
const PREMIUM_USERS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  // Add any additional premium user emails here
];

/**
 * Synchronously checks if a user is premium based on email or localStorage
 * This is a fallback for components that can't handle async checks
 */
export function isUserPremium(user: User | null): boolean {
  if (!user || !user.email) return false;

  // First check if we have a cached premium status in localStorage
  try {
    const cachedStatus = localStorage.getItem(`premium_status_${user.id}`);
    if (cachedStatus) {
      const statusData = JSON.parse(cachedStatus);
      // Check if the cached status is still valid (not expired)
      if (statusData.expiresAt && new Date(statusData.expiresAt) > new Date()) {
        return statusData.isPremium;
      }
    }
  } catch (error) {
    console.warn("Error checking cached premium status:", error);
    // Continue to fallback check
  }

  // Fallback to hardcoded list
  return PREMIUM_USERS.includes((user.email || '').toLowerCase());
}

/**
 * Determines if a user can access a specific topic (synchronous version)
 * This uses the hardcoded premium user list for immediate checks
 *
 * @param topicTitle The title of the topic
 * @param topicId The ID of the topic (optional, for future use)
 * @param user The current user (null if unauthenticated)
 * @returns boolean indicating if the user can access the topic
 */
export function canAccessTopicSync(topicTitle: string, topicId: string | null, user: User | null): boolean {
  // Check if topic is in the public list (accessible to everyone)
  if (PUBLIC_TOPICS.includes(topicTitle)) {
    return true;
  }

  // If user is not authenticated, they can only access public topics
  if (!user) {
    return false;
  }

  // Check if topic is in the authenticated-only list (free for registered users)
  if (AUTHENTICATED_TOPICS.includes(topicTitle)) {
    return true;
  }

  // For premium topics, check if user is premium
  if (isUserPremium(user)) {
    return true;
  }

  // Coupon access
  if (hasCouponAccess(user, topicTitle)) {
    return true;
  }

  return false;
}

/**
 * Determines if a user can access a specific topic (async version)
 * This checks the database for subscription status
 *
 * @param topicTitle The title of the topic
 * @param topicId The ID of the topic (optional, for future use)
 * @param user The current user (null if unauthenticated)
 * @returns Promise<boolean> indicating if the user can access the topic
 */
export async function canAccessTopic(topicTitle: string, topicId: string | null, user: User | null): Promise<boolean> {
  // Check if topic is in the public list (accessible to everyone)
  if (PUBLIC_TOPICS.includes(topicTitle)) {
    return true;
  }

  // If user is not authenticated, they can only access public topics
  if (!user) {
    return false;
  }

  // Check if topic is in the authenticated-only list (free for registered users)
  if (AUTHENTICATED_TOPICS.includes(topicTitle)) {
    return true;
  }

  // For premium topics, check if user is subscribed
  const isSubscribed = await isUserSubscribed(user);

  // Cache the premium status in localStorage for synchronous checks
  if (user && user.id) {
    try {
      // Get subscription details to include expiration date
      // Use 'any' to bypass type errors for dynamic or missing tables
      const { data: subscription } = await (supabase as any)
        .from('subscriptions')
        .select('end_date')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('end_date', { ascending: false })
        .limit(1);

      const expiresAt = subscription && subscription.length > 0
        ? subscription[0].end_date
        : new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(); // Default to 24 hours from now

      localStorage.setItem(`premium_status_${user.id}`, JSON.stringify({
        isPremium: isSubscribed,
        expiresAt: expiresAt
      }));
    } catch (error) {
      console.warn("Error caching premium status:", error);
      // Continue anyway
    }
  }

  // Coupon access
  if (hasCouponAccess(user, topicTitle)) {
    return true;
  }

  return isSubscribed;
}

/**
 * Determines if a topic should be marked as premium
 *
 * @param topicTitle The title of the topic
 * @param difficulty The difficulty of the topic (optional)
 * @returns boolean indicating if the topic should be marked as premium
 */
export function isTopicPremium(topicTitle: string, difficulty?: string | null): boolean {
  // Public topics are never premium
  if (PUBLIC_TOPICS.includes(topicTitle)) {
    return false;
  }

  // Authenticated-only topics are not premium
  if (AUTHENTICATED_TOPICS.includes(topicTitle)) {
    return false;
  }

  // All other topics are premium
  return true;
}

// --- Coupon Feature ---
const VALID_COUPONS = ["GRC0125"];

export function hasCouponAccess(user: User | null, topicTitle: string): boolean {
  if (!user || topicTitle !== "Cybersecurity Foundation - Easy") return false;
  const redeemed = localStorage.getItem(`coupon_${user.id}_${topicTitle}`);
  return redeemed === "true";
}

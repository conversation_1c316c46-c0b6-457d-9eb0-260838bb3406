import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Plus, Trash } from "lucide-react";
import { sanitizeInput } from "@/utils/security";

// Utility function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};

interface AdminQuestionFormProps {
  initialData?: any;
  onSuccess: () => void;
  onCancel: () => void;
  topics: any[];
}

const AdminQuestionForm = ({ initialData, onSuccess, onCancel, topics }: AdminQuestionFormProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [questionText, setQuestionText] = useState("");
  const [explanation, setExplanation] = useState("");
  const [topicId, setTopicId] = useState("");
  const [difficulty, setDifficulty] = useState("medium");
  const [options, setOptions] = useState<string[]>(["", "", "", ""]);
  const [correctAnswer, setCorrectAnswer] = useState<number>(0);

  useEffect(() => {
    if (initialData) {
      setQuestionText(decodeHtmlEntities(initialData.question_text || ""));
      setExplanation(decodeHtmlEntities(initialData.explanation || ""));
      setTopicId(initialData.topic_id || "");
      setDifficulty(initialData.difficulty || "medium");

      // Handle options which could be in different formats
      let parsedOptions: string[] = [];
      try {
        if (Array.isArray(initialData.options)) {
          parsedOptions = initialData.options.map((opt: any) => decodeHtmlEntities(String(opt)));
        } else if (typeof initialData.options === 'object' && initialData.options !== null) {
          parsedOptions = Object.values(initialData.options).map((opt: any) => decodeHtmlEntities(String(opt)));
        } else if (typeof initialData.options === 'string') {
          const parsed = JSON.parse(initialData.options);
          parsedOptions = Object.values(parsed).map((opt: any) => decodeHtmlEntities(String(opt)));
        }
      } catch (error) {
        console.error("Error parsing options:", error);
        parsedOptions = ["", "", "", ""];
      }

      // Ensure we have at least 4 options
      while (parsedOptions.length < 4) {
        parsedOptions.push("");
      }

      setOptions(parsedOptions);

      // Parse correct answer
      let parsedCorrectAnswer = 0;
      try {
        parsedCorrectAnswer = typeof initialData.correct_answer === 'number'
          ? initialData.correct_answer
          : parseInt(initialData.correct_answer);

        if (isNaN(parsedCorrectAnswer)) parsedCorrectAnswer = 0;
      } catch (error) {
        console.error("Error parsing correct answer:", error);
      }

      setCorrectAnswer(parsedCorrectAnswer);
    }
  }, [initialData]);

  const handleOptionChange = (index: number, value: string) => {
    const newOptions = [...options];
    newOptions[index] = value;
    setOptions(newOptions);
  };

  const addOption = () => {
    setOptions([...options, ""]);
  };

  const removeOption = (index: number) => {
    if (options.length <= 2) {
      toast({
        title: "Cannot remove option",
        description: "A question must have at least 2 options",
        variant: "destructive",
      });
      return;
    }

    const newOptions = options.filter((_, i) => i !== index);
    setOptions(newOptions);

    // Adjust correct answer if needed
    if (correctAnswer === index) {
      setCorrectAnswer(0);
    } else if (correctAnswer > index) {
      setCorrectAnswer(correctAnswer - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Sanitize inputs
    const sanitizedQuestionText = sanitizeInput(questionText.trim());
    const sanitizedExplanation = sanitizeInput(explanation.trim());
    const sanitizedOptions = options.map(opt => sanitizeInput(opt.trim()));

    // Validate form
    if (!sanitizedQuestionText) {
      toast({
        title: "Missing question text",
        description: "Please enter the question text",
        variant: "destructive",
      });
      return;
    }

    if (!topicId) {
      toast({
        title: "Missing topic",
        description: "Please select a topic for this question",
        variant: "destructive",
      });
      return;
    }

    // Check if all options have content
    const emptyOptions = sanitizedOptions.filter(opt => !opt).length;
    if (emptyOptions > 0) {
      toast({
        title: "Empty options",
        description: `You have ${emptyOptions} empty option(s). Please fill all options.`,
        variant: "destructive",
      });
      return;
    }

    // Format options as an object for Supabase
    const optionsObject: Record<string, string> = {};
    sanitizedOptions.forEach((opt, index) => {
      optionsObject[index.toString()] = opt;
    });

    setLoading(true);

    try {
      if (initialData) {
        // Update existing question
        const { error } = await supabase
          .from("questions")
          .update({
            question_text: sanitizedQuestionText,
            options: optionsObject,
            correct_answer: correctAnswer.toString(),
            explanation: sanitizedExplanation,
            topic_id: topicId,
            difficulty,
            updated_at: new Date().toISOString(),
          })
          .eq("id", initialData.id);

        if (error) throw error;

        toast({
          title: "Question updated",
          description: "The question has been successfully updated",
        });
      } else {
        // Create new question
        const { error } = await supabase
          .from("questions")
          .insert({
            question_text: sanitizedQuestionText,
            options: optionsObject,
            correct_answer: correctAnswer.toString(),
            explanation: sanitizedExplanation,
            topic_id: topicId,
            difficulty,
          });

        if (error) throw error;

        toast({
          title: "Question created",
          description: "The question has been successfully created",
        });
      }

      onSuccess();
    } catch (error: any) {
      console.error("Error saving question:", error);
      toast({
        title: "Error saving question",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 mt-4 max-h-[65vh] overflow-visible">
      <div className="space-y-2">
        <Label htmlFor="topic">Topic</Label>
        <Select value={topicId} onValueChange={setTopicId}>
          <SelectTrigger>
            <SelectValue placeholder="Select a topic" />
          </SelectTrigger>
          <SelectContent>
            {topics.map((topic) => (
              <SelectItem key={topic.id} value={topic.id.toString()}>
                {topic.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="difficulty">Difficulty</Label>
        <Select value={difficulty} onValueChange={setDifficulty}>
          <SelectTrigger>
            <SelectValue placeholder="Select difficulty" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="easy">Easy</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="hard">Hard</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="questionText">Question Text</Label>
        <Textarea
          id="questionText"
          value={questionText}
          onChange={(e) => setQuestionText(e.target.value)}
          placeholder="Enter the question text"
          className="min-h-[100px]"
        />
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label>Answer Options</Label>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={addOption}
            className="h-8"
          >
            <Plus className="h-4 w-4 mr-1" /> Add Option
          </Button>
        </div>

        <div className="space-y-3">
          {options.map((option, index) => (
            <div key={index} className="flex items-start gap-2">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <input
                    type="radio"
                    id={`correct-${index}`}
                    name="correctAnswer"
                    checked={correctAnswer === index}
                    onChange={() => setCorrectAnswer(index)}
                    className="h-4 w-4 text-cyber-primary"
                  />
                  <Label htmlFor={`correct-${index}`} className="text-sm font-normal">
                    Correct Answer
                  </Label>
                </div>
                <div className="flex gap-2">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-muted flex items-center justify-center mt-2">
                    {String.fromCharCode(65 + index)}
                  </div>
                  <Input
                    value={option}
                    onChange={(e) => handleOptionChange(index, e.target.value)}
                    placeholder={`Option ${String.fromCharCode(65 + index)}`}
                    className="flex-1"
                  />
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => removeOption(index)}
                className="h-10 w-10 mt-6"
              >
                <Trash className="h-4 w-4 text-red-500" />
              </Button>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="explanation">Explanation (Optional)</Label>
        <Textarea
          id="explanation"
          value={explanation}
          onChange={(e) => setExplanation(e.target.value)}
          placeholder="Explain why the correct answer is right"
          className="min-h-[100px]"
        />
      </div>

      <div className="flex justify-end gap-2 pt-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {initialData ? "Update Question" : "Create Question"}
        </Button>
      </div>
    </form>
  );
};

export default AdminQuestionForm;

// Test script for authentication email delivery
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import readline from 'readline';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env
dotenv.config({ path: resolve(__dirname, '../.env') });

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY is not set in .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for email
function promptForEmail() {
  return new Promise((resolve) => {
    rl.question('Enter your email address to test authentication email: ', (email) => {
      resolve(email);
    });
  });
}

// Function to prompt for test type
function promptForTestType() {
  return new Promise((resolve) => {
    console.log('\nSelect test type:');
    console.log('1. Password Reset Email');
    console.log('2. Sign Up Email');
    console.log('3. Magic Link Email');
    rl.question('Enter your choice (1-3): ', (choice) => {
      resolve(choice);
    });
  });
}

// Main function
async function testAuthEmail() {
  console.log('=== Authentication Email Test ===');
  
  try {
    const email = await promptForEmail();
    const testType = await promptForTestType();
    
    const appUrl = process.env.VITE_APP_URL || 'https://secquiz.app';
    
    let result;
    
    switch (testType) {
      case '1':
        console.log(`\nSending password reset email to ${email}...`);
        result = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${appUrl}/auth/reset-password`,
        });
        break;
      
      case '2':
        console.log(`\nSending sign up email to ${email}...`);
        result = await supabase.auth.signUp({
          email,
          password: 'TemporaryPassword123!',
          options: {
            emailRedirectTo: `${appUrl}/auth/verify`,
          }
        });
        break;
      
      case '3':
        console.log(`\nSending magic link email to ${email}...`);
        result = await supabase.auth.signInWithOtp({
          email,
          options: {
            emailRedirectTo: `${appUrl}/auth/callback`,
          }
        });
        break;
      
      default:
        console.error('Invalid choice. Please select 1, 2, or 3.');
        rl.close();
        return;
    }
    
    if (result.error) {
      console.error('\nError sending email:', result.error.message);
    } else {
      console.log('\nEmail request sent successfully!');
      console.log(`Please check ${email} for the authentication email.`);
      console.log('If you don\'t receive it within 2 minutes, check your spam folder.');
      console.log('Also check the Resend dashboard to confirm the email was sent.');
    }
  } catch (err) {
    console.error('Unexpected error:', err);
  } finally {
    rl.close();
  }
}

// Run the test
testAuthEmail();

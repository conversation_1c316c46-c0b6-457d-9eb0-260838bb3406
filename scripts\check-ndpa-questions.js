// Script to check NDPR topic quiz questions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkQuestions() {
  try {
    // Find the NDPR topic
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('id, title')
      .ilike('title', '%NDPR%');

    if (topicsError) {
      console.error('Error fetching topics:', topicsError);
      return;
    }

    if (!topics || topics.length === 0) {
      console.error('Could not find NDPA topic');
      return;
    }

    console.log(`Found ${topics.length} NDPA topics:`);
    topics.forEach(topic => console.log(`- ${topic.title} (${topic.id})`));

    // Process each topic
    for (const topic of topics) {
      console.log(`\nChecking questions for topic: ${topic.title}`);

      // Get all questions for this topic
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('id, question_text, options, correct_answer, explanation')
        .eq('topic_id', topic.id);

      if (questionsError) {
        console.error(`Error fetching questions for topic ${topic.title}:`, questionsError);
        continue;
      }

      console.log(`Found ${questions.length} questions for topic ${topic.title}`);

      // Check for potential issues with correct_answer format
      const potentialIssues = questions.filter(q => {
        // Check if correct_answer is a number (as string) but options use letters
        if (['0', '1', '2', '3'].includes(q.correct_answer) &&
            q.options &&
            Object.keys(q.options).some(k => ['A', 'B', 'C', 'D'].includes(k))) {
          return true;
        }

        // Check if correct_answer is a letter but options use numbers
        if (['A', 'B', 'C', 'D'].includes(q.correct_answer) &&
            q.options &&
            Object.keys(q.options).some(k => ['0', '1', '2', '3'].includes(k))) {
          return true;
        }

        return false;
      });

      if (potentialIssues.length > 0) {
        console.log(`\nFound ${potentialIssues.length} questions with potential answer format issues:`);
        potentialIssues.forEach(q => {
          console.log(`- Question ID: ${q.id}`);
          console.log(`  Text: ${q.question_text.substring(0, 100)}...`);
          console.log(`  Correct answer: ${q.correct_answer}`);
          console.log(`  Option keys: ${Object.keys(q.options).join(', ')}`);
        });
      }

      // Display each question
      for (const question of questions) {
        console.log('\n' + '-'.repeat(80));
        console.log(`Question: ${question.question_text}`);
        console.log(`Correct answer: ${question.correct_answer}`);

        // Display options
        console.log('\nOptions:');
        for (const [key, value] of Object.entries(question.options)) {
          const isCorrect = key === question.correct_answer ? ' (CORRECT)' : '';
          console.log(`  ${key}: ${value}${isCorrect}`);
        }

        console.log(`\nExplanation: ${question.explanation}`);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
checkQuestions();

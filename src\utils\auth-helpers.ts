import { User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { Tables } from "@/types/supabase";

// Array of admin user IDs - in a real app this would come from the database
// For demo purposes, you would add your test user ID here
const ADMIN_USER_IDS = [
  // Add test admin user IDs here
  // e.g. "d4c2b0a8-62e0-41e5-9b2d-8e1c9e3e4f5a"
];

// Array of admin emails for easier testing
const ADMIN_EMAILS = [
  "<EMAIL>"
];

// Array of subscribed (paid) user emails
const SUBSCRIBED_EMAILS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
];

/**
 * Check if a user has admin privileges
 * This checks both hardcoded admin emails and the database
 */
export async function isUserAdmin(user: User | null): Promise<boolean> {
  if (!user) return false;

  // First check hardcoded lists for development/demo purposes
  // Check if the user's ID is in the admin list or email is in admin emails (case-insensitive)
  if (ADMIN_USER_IDS.includes(user.id)) return true;

  if (user.email) {
    const userEmail = (user.email || '').toLowerCase();
    if (ADMIN_EMAILS.some(email => email.toLowerCase() === userEmail)) {
      return true;
    }
  }

  try {
    // Check profiles table for admin status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .maybeSingle();

    if (profile && profile.is_admin) {
      return true;
    }
  } catch (error) {
    console.error('Error checking admin status:', error);
  }

  return false;
}

/**
 * Check if a user has a paid subscription
 * This checks the subscriptions table in the database
 * and verifies that the subscription has not expired
 */
export async function isUserSubscribed(user: User | null): Promise<boolean> {
  if (!user) return false;

  try {
    // First check the hardcoded list for development/demo purposes
    if (user.email) {
      const userEmail = (user.email || '').toLowerCase();
      if (SUBSCRIBED_EMAILS.some(email => email.toLowerCase() === userEmail)) {
        return true;
      }
    }

    // Check the profiles table for subscription status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('subscription_ends_at, subscription_status')
      .eq('id', user.id)
      .maybeSingle();

    if (profile && profile.subscription_ends_at) {
      const endDate = new Date(profile.subscription_ends_at);
      const now = new Date();

      if (endDate > now) {
        return true; // Active subscription
      } else {
        // If subscription has expired, update it to inactive
        try {
          await supabase
            .from('profiles')
            .update({
              subscription_status: 'inactive',
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);
        } catch (updateError) {
          console.warn('Error updating subscription status:', updateError);
          // Continue anyway
        }
      }
    }

    // Check the payments table as a last fallback
    try {
      const { data, error } = await supabase
        .from('payments')
        .select("*")
        .eq("user_id", user.id)
        .eq("status", "completed")
        .order("created_at", { ascending: false })
        .limit(1);

      if (!error && data && data.length > 0) {
        return data[0].status === 'completed';
      }
    } catch (subError) {
      console.error('Error checking payments table:', subError);
      // Continue anyway, we've already checked other tables
    }

    return false;
  } catch (error) {
    console.error('Error checking subscription:', error);
    return false;
  }
}

/**
 * Get the subscription status of a user
 */
export async function getUserSubscriptionStatus(user: User | null): Promise<"Free" | "Premium" | "Expired"> {
  if (!user) return "Free";

  // Check if user is subscribed (active subscription)
  const subscribed = await isUserSubscribed(user);
  if (subscribed) return "Premium";

  // If not subscribed, check if they had a subscription that expired
  try {
    // Check profiles table for expired status
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('subscription_ends_at')
      .eq('id', user.id)
      .maybeSingle();

    if (profile && profile.subscription_ends_at) {
      const endDate = new Date(profile.subscription_ends_at);
      const now = new Date();

      if (endDate <= now) {
        return "Expired";
      }
    }
  } catch (error) {
    console.error('Error checking expired status:', error);
  }

  return "Free";
}

/**
 * Get the subscription details of a user
 */
export async function getUserSubscriptionDetails(user: User | null) {
  if (!user) return null;

  try {
    // First check if profiles table exists
    try {
      // First check profiles table
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .maybeSingle();

      if (!profileError && profile) {
        const now = new Date();
        const expirationDate = profile.subscription_ends_at ? new Date(profile.subscription_ends_at) : null;
        const isExpired = expirationDate && expirationDate <= now;

        return {
          status: profile.is_admin ? 'admin' : (isExpired ? 'expired' : 'free'),
          expiresAt: profile.subscription_ends_at,
          isActive: profile.subscription_status === 'active' && (!expirationDate || expirationDate > now),
          isExpired: isExpired
        };
      }
    } catch (profileTableError) {
      console.warn('Error accessing profiles table:', profileTableError);
      // Continue to next check
    }

    // Check if payments table exists
    try {
      // Check payments table
      const { data: payment, error: paymentError } = await supabase
        .from('payments')
        .select("*")
        .eq("user_id", user.id)
        .eq("status", "completed")
        .order("created_at", { ascending: false })
        .limit(1);

      if (!paymentError && payment && payment.length > 0) {
        return {
          status: payment[0].status === 'completed' ? 'premium' : 'free',
          paymentId: payment[0].id,
          createdAt: payment[0].created_at,
          isActive: payment[0].status === 'completed'
        };
      }
    } catch (paymentTableError) {
      console.warn('Error accessing payments table:', paymentTableError);
      // Continue to next check
    }

    // Fallback to feedback (assuming feedback table exists and has relevant info)
    try {
      const { data: feedback, error: feedbackError } = await supabase
        .from('feedback')
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(1);

      if (!feedbackError && feedback && feedback.length > 0) {
        return {
          status: 'premium', // Assuming feedback implies a paid subscription
          createdAt: feedback[0].created_at,
          isActive: true,
          isExpired: false
        };
      }
    } catch (feedbackTableError) {
      console.warn('Error accessing feedback table:', feedbackTableError);
      // Continue to next check
    }

    return {
      status: 'free',
      isActive: false,
      isExpired: false
    };
  } catch (error) {
    console.error('Error getting subscription details:', error);
    return null;
  }
}

/**
 * Helper for handling the free questions quota for premium content
 * Note: This limit only applies to premium content.
 * Registered users have unlimited access to free courses.
 */
export const FREE_QUESTIONS_LIMIT = 10;

export function getRemainingFreeQuestions(): number {
  const used = getUsedFreeQuestions();
  return Math.max(0, FREE_QUESTIONS_LIMIT - used);
}

export function getUsedFreeQuestions(): number {
  try {
    const used = parseInt(localStorage.getItem('free_questions_used') || '0', 10);
    return isNaN(used) ? 0 : used;
  } catch (e) {
    return 0;
  }
}

export function incrementFreeQuestionsUsed(): void {
  try {
    const current = getUsedFreeQuestions();
    localStorage.setItem('free_questions_used', (current + 1).toString());
  } catch (e) {
    console.error("Could not update local storage", e);
  }
}

export function resetFreeQuestionsUsed(): void {
  try {
    localStorage.setItem('free_questions_used', '0');
  } catch (e) {
    console.error("Could not reset local storage", e);
  }
}

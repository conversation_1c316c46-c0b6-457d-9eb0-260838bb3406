import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file in the parent directory
const envPath = join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error('.env file not found at:', envPath);
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixLearningMaterials() {
  try {
    console.log('Fixing learning materials...');
    
    // Get the specific materials we're interested in
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*')
      .in('id', ['d19b40dc-6b1b-432a-855e-641f36480af9', 'f4b448b3-94f1-465c-858b-364ff2828337']);
    
    if (error) {
      console.error('Error querying learning materials:', error);
      return;
    }
    
    for (const material of data) {
      console.log(`\nProcessing material: ${material.title}`);
      
      // Check if content exists
      if (!material.content) {
        console.log('No content found for this material');
        continue;
      }
      
      // Fix the content by extracting the body content
      let fixedContent = material.content;
      
      // If it's a full HTML document, extract just the body content
      if (material.content.includes('<!DOCTYPE html>')) {
        const bodyStartMatch = material.content.match(/<body[^>]*>([\s\S]*)<\/body>/i);
        
        if (bodyStartMatch && bodyStartMatch[1]) {
          fixedContent = bodyStartMatch[1].trim();
          console.log('Extracted body content from HTML document');
        } else {
          console.log('Could not extract body content, document structure may be invalid');
        }
      }
      
      // Update the material with the fixed content
      const { error: updateError } = await supabase
        .from('learning_materials')
        .update({ content: fixedContent })
        .eq('id', material.id);
      
      if (updateError) {
        console.error(`Error updating material ${material.id}:`, updateError);
      } else {
        console.log(`Successfully updated material: ${material.title}`);
      }
    }
    
    console.log('\nFinished fixing learning materials');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixLearningMaterials();

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { X, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

const InstallPWA = () => {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showInstallBanner, setShowInstallBanner] = useState(false);

  useEffect(() => {
    // Check if the app is already installed
    const isAppInstalled = window.matchMedia('(display-mode: standalone)').matches;

    // Don't show the banner if the app is already installed
    if (isAppInstalled) {
      return;
    }

    // Store the install prompt event for later use
    const handleBeforeInstallPrompt = (e: Event) => {
      // We need to prevent default to be able to show our custom UI later
      // But we should NOT prevent default if we're not going to show our custom UI
      e.preventDefault();

      // Store the event for later use
      setInstallPrompt(e as BeforeInstallPromptEvent);

      // Check if the user has previously dismissed the banner
      const hasUserDismissedBanner = localStorage.getItem('pwa-install-dismissed');
      if (!hasUserDismissedBanner) {
        setShowInstallBanner(true);
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

    // Check if the user has previously dismissed the banner
    const hasUserDismissedBanner = localStorage.getItem('pwa-install-dismissed');
    if (hasUserDismissedBanner) {
      setShowInstallBanner(false);
    }

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!installPrompt) {
      // If we don't have the prompt event stored, try to show native prompt
      // This is a fallback and may not work in all browsers
      alert('Installation prompt not available. Please use your browser\'s "Add to Home Screen" feature.');
      return;
    }

    try {
      // Show the install prompt
      await installPrompt.prompt();

      // Wait for the user to respond to the prompt
      const choiceResult = await installPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        // Only log in development
        if (process.env.NODE_ENV !== 'production') {
          console.log('User accepted the install prompt');
        }
      } else {
        // Store dismissal in localStorage to avoid showing again too soon
        localStorage.setItem('pwa-install-dismissed', 'true');

        // Only log in development
        if (process.env.NODE_ENV !== 'production') {
          console.log('User dismissed the install prompt');
        }
      }
    } catch (error) {
      // Handle any errors that might occur during the prompt
      console.error('Error showing install prompt:', error);
    } finally {
      // Reset the install prompt
      setInstallPrompt(null);
      setShowInstallBanner(false);
    }
  };

  const handleDismiss = () => {
    // Store the user's preference in localStorage
    localStorage.setItem('pwa-install-dismissed', 'true');
    setShowInstallBanner(false);
  };

  if (!showInstallBanner) {
    return null;
  }

  return (
    <motion.div
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 100, opacity: 0 }}
      className="fixed bottom-0 left-0 right-0 p-4 z-50"
    >
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 mx-auto max-w-md border border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="w-10 h-10 mr-3" />
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-white">Install SecQuiz App</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Add to your home screen for a better experience
              </p>
            </div>
          </div>
          <button
            onClick={handleDismiss}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="mt-4 flex justify-end space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDismiss}
          >
            Not Now
          </Button>
          <Button
            size="sm"
            onClick={handleInstallClick}
            className="bg-cyber-primary hover:bg-cyber-primary/90 text-white"
          >
            <Download className="h-4 w-4 mr-2" />
            Install
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default InstallPWA;

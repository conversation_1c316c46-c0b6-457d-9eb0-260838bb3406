-- Fix User Data Synchronization Issues
-- This migration ensures proper user profile creation and data flow

-- First, ensure all required tables exist with proper structure
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id TEXT NOT NULL,
  amount_paid DECIMAL(10, 2) NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_payment_reference TEXT,
  subscription_code TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.payments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  amount DECIMAL(10, 2) NOT NULL,
  currency TEXT DEFAULT 'NGN',
  status TEXT NOT NULL,
  provider TEXT DEFAULT 'paystack',
  provider_payment_id TEXT,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.user_quiz_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  topic_id UUID REFERENCES topics(id) ON DELETE SET NULL,
  score INTEGER NOT NULL,
  total_questions INTEGER NOT NULL,
  percentage DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE 
      WHEN total_questions > 0 THEN (score::decimal / total_questions::decimal) * 100
      ELSE 0
    END
  ) STORED,
  time_taken INTEGER, -- in seconds
  answers JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_quiz_results ENABLE ROW LEVEL SECURITY;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Create improved function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into user_profiles
  INSERT INTO public.user_profiles (user_id, is_subscribed, is_admin)
  VALUES (NEW.id, false, false)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to handle updated_at timestamps
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON public.user_profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

DROP TRIGGER IF EXISTS update_subscriptions_updated_at ON public.subscriptions;
CREATE TRIGGER update_subscriptions_updated_at
  BEFORE UPDATE ON public.subscriptions
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

DROP TRIGGER IF EXISTS update_payments_updated_at ON public.payments;
CREATE TRIGGER update_payments_updated_at
  BEFORE UPDATE ON public.payments
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Backfill existing users with profiles
INSERT INTO public.user_profiles (user_id, is_subscribed, is_admin)
SELECT id, false, false FROM auth.users
WHERE id NOT IN (SELECT user_id FROM public.user_profiles)
ON CONFLICT (user_id) DO NOTHING;

-- Create RLS policies for user_profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
CREATE POLICY "Users can update their own profile"
  ON public.user_profiles FOR UPDATE
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
CREATE POLICY "Admins can view all profiles"
  ON public.user_profiles FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up
      WHERE up.user_id = auth.uid() AND up.is_admin = true
    )
  );

-- Create RLS policies for subscriptions
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.subscriptions;
CREATE POLICY "Users can view their own subscriptions"
  ON public.subscriptions FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all subscriptions" ON public.subscriptions;
CREATE POLICY "Admins can view all subscriptions"
  ON public.subscriptions FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up
      WHERE up.user_id = auth.uid() AND up.is_admin = true
    )
  );

-- Create RLS policies for payments
DROP POLICY IF EXISTS "Users can view their own payments" ON public.payments;
CREATE POLICY "Users can view their own payments"
  ON public.payments FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all payments" ON public.payments;
CREATE POLICY "Admins can view all payments"
  ON public.payments FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up
      WHERE up.user_id = auth.uid() AND up.is_admin = true
    )
  );

-- Create RLS policies for user_quiz_results
DROP POLICY IF EXISTS "Users can view their own quiz results" ON public.user_quiz_results;
CREATE POLICY "Users can view their own quiz results"
  ON public.user_quiz_results FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own quiz results" ON public.user_quiz_results;
CREATE POLICY "Users can insert their own quiz results"
  ON public.user_quiz_results FOR INSERT
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all quiz results" ON public.user_quiz_results;
CREATE POLICY "Admins can view all quiz results"
  ON public.user_quiz_results FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up
      WHERE up.user_id = auth.uid() AND up.is_admin = true
    )
  );

-- Create function to record quiz results
CREATE OR REPLACE FUNCTION public.record_quiz_result(
  p_topic_id UUID,
  p_score INTEGER,
  p_total_questions INTEGER,
  p_time_taken INTEGER DEFAULT NULL,
  p_answers JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  result_id UUID;
BEGIN
  INSERT INTO public.user_quiz_results (
    user_id, topic_id, score, total_questions, time_taken, answers
  ) VALUES (
    auth.uid(), p_topic_id, p_score, p_total_questions, p_time_taken, p_answers
  ) RETURNING id INTO result_id;
  
  -- Also update user_progress table if it exists
  INSERT INTO public.user_progress (user_id, topic_id, score, max_score)
  VALUES (auth.uid(), p_topic_id, p_score, p_total_questions)
  ON CONFLICT DO NOTHING;
  
  RETURN result_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to handle payment success
CREATE OR REPLACE FUNCTION public.handle_payment_success(
  p_user_email TEXT,
  p_plan_id TEXT,
  p_amount DECIMAL,
  p_reference TEXT
)
RETURNS JSONB AS $$
DECLARE
  user_record RECORD;
  subscription_id UUID;
  payment_id UUID;
  start_date TIMESTAMPTZ;
  end_date TIMESTAMPTZ;
  result JSONB;
BEGIN
  -- Get user by email
  SELECT id INTO user_record FROM auth.users WHERE email = p_user_email;
  
  IF user_record IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'User not found');
  END IF;
  
  -- Calculate subscription dates
  start_date := NOW();
  CASE p_plan_id
    WHEN 'basic' THEN end_date := start_date + INTERVAL '1 month';
    WHEN 'pro' THEN end_date := start_date + INTERVAL '3 months';
    WHEN 'elite' THEN end_date := start_date + INTERVAL '1 year';
    ELSE end_date := start_date + INTERVAL '1 month';
  END CASE;
  
  -- Record payment
  INSERT INTO public.payments (
    user_id, amount, status, provider_payment_id, metadata
  ) VALUES (
    user_record.id, p_amount, 'completed', p_reference,
    jsonb_build_object('plan_id', p_plan_id, 'reference', p_reference)
  ) RETURNING id INTO payment_id;
  
  -- Create or update subscription
  INSERT INTO public.subscriptions (
    user_id, plan_id, amount_paid, start_date, end_date, 
    is_active, last_payment_reference
  ) VALUES (
    user_record.id, p_plan_id, p_amount, start_date, end_date,
    true, p_reference
  ) ON CONFLICT (user_id) DO UPDATE SET
    plan_id = EXCLUDED.plan_id,
    amount_paid = EXCLUDED.amount_paid,
    start_date = EXCLUDED.start_date,
    end_date = EXCLUDED.end_date,
    is_active = true,
    last_payment_reference = EXCLUDED.last_payment_reference,
    updated_at = NOW()
  RETURNING id INTO subscription_id;
  
  -- Update user profile
  UPDATE public.user_profiles SET
    is_subscribed = true,
    subscription_expires_at = end_date,
    updated_at = NOW()
  WHERE user_id = user_record.id;
  
  RETURN jsonb_build_object(
    'success', true,
    'payment_id', payment_id,
    'subscription_id', subscription_id,
    'expires_at', end_date
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

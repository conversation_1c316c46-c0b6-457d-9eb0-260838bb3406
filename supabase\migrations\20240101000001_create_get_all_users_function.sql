-- Create a function to get all users with their profile information
-- This function should be executed with admin privileges
CREATE OR REPLACE FUNCTION public.get_all_users()
RETURNS TABLE (
  id uuid,
  email text,
  created_at timestamptz,
  last_sign_in_at timestamptz,
  is_subscribed boolean,
  is_admin boolean,
  subscription_expires_at timestamptz
)
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user_profiles table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'user_profiles'
  ) THEN
    -- If user_profiles exists, join with it
    RETURN QUERY
    SELECT
      au.id,
      au.email,
      au.created_at,
      au.last_sign_in_at,
      COALESCE(up.is_subscribed, false) as is_subscribed,
      COALESCE(up.is_admin, false) as is_admin,
      up.subscription_expires_at
    FROM auth.users au
    LEFT JOIN public.user_profiles up ON au.id = up.user_id
    ORDER BY au.created_at DESC;
  ELSE
    -- If user_profiles doesn't exist, return users with default values
    RETURN QUERY
    SELECT
      au.id,
      au.email,
      au.created_at,
      au.last_sign_in_at,
      false as is_subscribed,
      false as is_admin,
      NULL::timestamptz as subscription_expires_at
    FROM auth.users au
    ORDER BY au.created_at DESC;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_all_users() TO authenticated;

-- Create a function to delete a user (admin only)
CREATE OR REPLACE FUNCTION public.delete_user(user_id uuid)
RETURNS void
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user_profiles table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'user_profiles'
  ) THEN
    -- Delete user profile first (due to foreign key constraints)
    DELETE FROM public.user_profiles WHERE user_id = $1;
  END IF;

  -- Delete user from auth.users (requires admin privileges)
  DELETE FROM auth.users WHERE id = $1;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_user(uuid) TO authenticated;

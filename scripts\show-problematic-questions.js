// Script to show the problematic NDPR questions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function showProblematicQuestions() {
  try {
    // The specific question IDs
    const questionIds = [
      'b818fc87-79df-47a7-9b6e-8472dbeedaec', // DPIA question
      'a15ded3b-0edb-4f8d-9dda-d44687f36119'  // DPO question
    ];
    
    // Get the questions
    const { data: questions, error } = await supabase
      .from('questions')
      .select('id, question_text, options, correct_answer, explanation')
      .in('id', questionIds);
    
    if (error) {
      console.error('Error fetching questions:', error);
      return;
    }
    
    // Display the questions
    questions.forEach(question => {
      console.log('\n' + '='.repeat(80));
      console.log(`ID: ${question.id}`);
      console.log(`Question: ${question.question_text}`);
      
      console.log('\nOptions:');
      for (const [key, value] of Object.entries(question.options)) {
        console.log(`  ${key}: ${value}`);
      }
      
      console.log(`\nCorrect Answer: ${question.correct_answer}`);
      console.log(`\nExplanation: ${question.explanation}`);
      console.log('='.repeat(80));
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
showProblematicQuestions();

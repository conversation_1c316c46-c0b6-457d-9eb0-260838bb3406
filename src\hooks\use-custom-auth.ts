import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

export function useCustomAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Sign up a user with email verification via Supabase
   */
  const signUp = async (
    email: string,
    password: string,
    metadata?: Record<string, unknown>
  ): Promise<{
    success: boolean;
    message?: string;
    error?: string;
    data?: unknown;
  }> => {
    setLoading(true);
    setError(null);

    try {
      // Sign up the user with Supabase (which handles email verification)
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (signUpError) throw signUpError;

      return {
        success: true,
        message: 'Account created! Please check your email to verify your account.',
        data,
      };
    } catch (err: unknown) {
      const message =
        err && typeof err === "object" && "message" in err
          ? String((err as { message?: string }).message)
          : "An error occurred during sign up";
      setError(message);
      return {
        success: false,
        error: message,
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Send a password reset email via Supabase
   */
  const resetPassword = async (
    email: string
  ): Promise<{
    success: boolean;
    message?: string;
    error?: string;
    data?: unknown;
  }> => {
    setLoading(true);
    setError(null);

    try {
      // Request password reset from Supabase
      const { data, error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (resetError) throw resetError;

      return {
        success: true,
        message: 'Password reset instructions sent to your email.',
        data,
      };
    } catch (err: unknown) {
      const message =
        err && typeof err === "object" && "message" in err
          ? String((err as { message?: string }).message)
          : "An error occurred during password reset";
      setError(message);
      return {
        success: false,
        error: message,
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    signUp,
    resetPassword,
  };
}

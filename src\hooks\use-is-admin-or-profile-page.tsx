import { useLocation } from "react-router-dom";

/**
 * Custom hook to check if the current page is the admin dashboard or profile page
 * @returns boolean indicating if the current page is admin or profile
 */
export function useIsAdminOrProfilePage(): boolean {
  const location = useLocation();
  const path = location.pathname;
  
  return path === "/admin" || path === "/profile";
}

export default useIsAdminOrProfilePage;

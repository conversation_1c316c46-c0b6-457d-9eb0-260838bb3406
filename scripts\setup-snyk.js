#!/usr/bin/env node

/**
 * This script helps users set up Snyk for local security scanning.
 * It checks if Sny<PERSON> is installed and authenticated, and guides the user through the setup process.
 */

import { execSync } from 'child_process';
import { createInterface } from 'readline';
import { existsSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';

const readline = createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = (query) => new Promise((resolve) => readline.question(query, resolve));

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

// Check if a command exists
const commandExists = (cmd) => {
  try {
    execSync(`which ${cmd}`, { stdio: 'ignore' });
    return true;
  } catch (e) {
    return false;
  }
};

// Check if Snyk is authenticated
const isSnykAuthenticated = () => {
  try {
    const configPath = join(homedir(), '.config', 'configstore', 'snyk.json');
    return existsSync(configPath);
  } catch (e) {
    return false;
  }
};

// Main function
async function main() {
  console.log(`\n${colors.bold}${colors.cyan}SecQuiz - Snyk Security Setup${colors.reset}\n`);
  console.log(`This script will help you set up Snyk for local security scanning.\n`);
  
  // Check if Snyk is installed
  const snykInstalled = commandExists('snyk');
  
  if (!snykInstalled) {
    console.log(`${colors.yellow}Snyk CLI is not installed.${colors.reset}`);
    const installGlobally = await question(`Would you like to install Snyk globally? (y/n): `);
    
    if (installGlobally.toLowerCase() === 'y') {
      console.log(`\n${colors.cyan}Installing Snyk globally...${colors.reset}`);
      try {
        execSync('npm install -g snyk', { stdio: 'inherit' });
        console.log(`\n${colors.green}Snyk installed successfully!${colors.reset}`);
      } catch (error) {
        console.error(`\n${colors.red}Failed to install Snyk:${colors.reset}`, error.message);
        console.log(`\nPlease try installing manually with: npm install -g snyk`);
        readline.close();
        return;
      }
    } else {
      console.log(`\n${colors.yellow}Skipping Snyk installation.${colors.reset}`);
      console.log(`You can install it later with: npm install -g snyk`);
      readline.close();
      return;
    }
  } else {
    console.log(`${colors.green}✓ Snyk CLI is already installed.${colors.reset}`);
  }
  
  // Check if Snyk is authenticated
  const authenticated = isSnykAuthenticated();
  
  if (!authenticated) {
    console.log(`\n${colors.yellow}You are not authenticated with Snyk.${colors.reset}`);
    console.log(`\nTo use Snyk, you need to authenticate with your Snyk account.`);
    console.log(`If you don't have an account, you can create one at https://snyk.io/\n`);
    
    const authenticate = await question(`Would you like to authenticate with Snyk now? (y/n): `);
    
    if (authenticate.toLowerCase() === 'y') {
      console.log(`\n${colors.cyan}Running Snyk authentication...${colors.reset}`);
      console.log(`A browser window will open. Please follow the instructions to authenticate.`);
      
      try {
        execSync('snyk auth', { stdio: 'inherit' });
        console.log(`\n${colors.green}Authentication successful!${colors.reset}`);
      } catch (error) {
        console.error(`\n${colors.red}Authentication failed:${colors.reset}`, error.message);
        console.log(`\nPlease try authenticating manually with: snyk auth`);
        readline.close();
        return;
      }
    } else {
      console.log(`\n${colors.yellow}Skipping Snyk authentication.${colors.reset}`);
      console.log(`You can authenticate later with: snyk auth`);
    }
  } else {
    console.log(`${colors.green}✓ You are already authenticated with Snyk.${colors.reset}`);
  }
  
  // Test Snyk
  console.log(`\n${colors.cyan}Testing Snyk configuration...${colors.reset}`);
  
  try {
    execSync('snyk --version', { stdio: 'pipe' });
    console.log(`${colors.green}✓ Snyk is properly configured.${colors.reset}`);
    
    console.log(`\n${colors.bold}Available Snyk commands:${colors.reset}`);
    console.log(`  ${colors.cyan}snyk test${colors.reset} - Test for vulnerabilities`);
    console.log(`  ${colors.cyan}snyk monitor${colors.reset} - Monitor your project on Snyk dashboard`);
    console.log(`  ${colors.cyan}snyk wizard${colors.reset} - Interactive wizard to fix vulnerabilities`);
    
    console.log(`\n${colors.bold}NPM scripts:${colors.reset}`);
    console.log(`  ${colors.cyan}npm run security:check${colors.reset} - Run basic security checks`);
    console.log(`  ${colors.cyan}npm run security:check:full${colors.reset} - Run comprehensive security checks`);
    console.log(`  ${colors.cyan}npm run security:fix${colors.reset} - Fix automatically fixable issues`);
    console.log(`  ${colors.cyan}npm run security:wizard${colors.reset} - Run Snyk wizard`);
    console.log(`  ${colors.cyan}npm run security:monitor${colors.reset} - Monitor your project on Snyk dashboard`);
  } catch (error) {
    console.error(`\n${colors.red}Snyk test failed:${colors.reset}`, error.message);
    console.log(`\nPlease check your Snyk installation and authentication.`);
  }
  
  console.log(`\n${colors.green}${colors.bold}Setup complete!${colors.reset}`);
  readline.close();
}

main().catch(error => {
  console.error(`\n${colors.red}An error occurred:${colors.reset}`, error);
  readline.close();
});

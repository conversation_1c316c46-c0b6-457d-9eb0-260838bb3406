-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.is_admin();

-- Create a new version of the function with better error handling
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  -- Check if admin_users table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'admin_users'
  ) THEN
    -- Check if user_id column exists in admin_users
    IF EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'admin_users' AND column_name = 'user_id'
    ) THEN
      -- Return true if the current user is in the admin_users table
      RETURN EXISTS (
        SELECT 1 FROM public.admin_users WHERE user_id = auth.uid()
      );
    END IF;
  END IF;

  -- If admin_users table doesn't exist or doesn't have user_id column,
  -- check profiles table as a fallback
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'profiles'
  ) THEN
    -- Check if is_admin column exists in profiles
    IF EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'is_admin'
    ) THEN
      -- Return true if the current user has is_admin = true in profiles
      RETURN EXISTS (
        SELECT 1 FROM public.profiles WHERE id = auth.uid() AND is_admin = true
      );
    END IF;
  END IF;

  -- If user_profiles table exists, check it as a last resort
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'user_profiles'
  ) THEN
    -- Check if is_admin column exists in user_profiles
    IF EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'is_admin'
    ) THEN
      -- Return true if the current user has is_admin = true in user_profiles
      RETURN EXISTS (
        SELECT 1 FROM public.user_profiles WHERE user_id = auth.uid() AND is_admin = true
      );
    END IF;
  END IF;

  -- Default to false if no admin tables exist or user is not in any of them
  RETURN false;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;

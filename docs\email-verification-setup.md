# Email Verification Setup Guide

This guide will help you set up reliable email verification for your SecQuiz application using EmailJS.

## Option 1: Configure Supabase SMTP (Recommended for Production)

1. **Update the Supabase Configuration**

   Edit the `supabase/config.toml` file with your SMTP provider details:

   ```toml
   # Email configuration for Supabase Auth
   [auth.email]
   # Email address used for sending emails
   sender_name = "SecQuiz"
   sender_email = "<EMAIL>"

   # SMTP configuration
   [auth.smtp]
   host = "smtp.example.com"  # Replace with your SMTP provider
   port = 587
   user = "your_smtp_username"  # Replace with your SMTP username
   pass = "your_smtp_password"  # Replace with your SMTP password
   admin_email = "<EMAIL>"  # Replace with your admin email
   max_frequency = 60  # Seconds between emails sent to the same address
   ```

2. **Restart Supabase**

   After updating the configuration, restart your Supabase instance:

   ```bash
   supabase stop
   supabase start
   ```

3. **Test the Configuration**

   Try signing up a new user to test if the verification emails are being sent correctly.

## Option 2: Set Up EmailJS for Custom Email Verification

1. **Create an EmailJS Account**

   - Go to [EmailJS](https://www.emailjs.com/) and create an account
   - Create a new service connected to your email provider (Gmail, Outlook, etc.)
   - Note your Service ID

2. **Create an Email Template**

   - In EmailJS, create a new template for verification emails
   - Include the following parameters in your template:
     - `{{to_email}}`: The recipient's email address
     - `{{verification_url}}`: The verification URL
     - `{{site_name}}`: The name of your site (SecQuiz)
   - Design your email with a clear call-to-action button linking to the verification URL
   - Note your Template ID

3. **Get Your User ID**

   - Go to Account > API Keys in EmailJS
   - Note your User ID (public key)

4. **Update the Email Service Configuration**

   Edit the `src/services/email-service.ts` file with your EmailJS details:

   ```typescript
   // EmailJS configuration
   const EMAILJS_SERVICE_ID = 'your_emailjs_service_id'; // Replace with your EmailJS service ID
   const EMAILJS_TEMPLATE_ID = 'your_emailjs_template_id'; // Replace with your EmailJS template ID
   const EMAILJS_USER_ID = 'your_emailjs_user_id'; // Replace with your EmailJS user ID
   ```

5. **Add EmailJS Script to index.html**

   Add the EmailJS script to your `index.html` file:

   ```html
   <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
   <script type="text/javascript">
     (function() {
       emailjs.init("your_emailjs_user_id"); // Replace with your EmailJS user ID
     })();
   </script>
   ```

## Recommended SMTP Providers

1. **Mailgun**
   - Free tier: 5,000 emails for 3 months, then pay-as-you-go
   - Excellent deliverability
   - Developer-friendly

2. **SendGrid**
   - Free tier: 100 emails per day
   - Good deliverability
   - Comprehensive analytics

3. **Zoho Mail**
   - Free tier available
   - Good deliverability
   - Integrated with Zoho suite

## Troubleshooting

If you're still experiencing issues with email delivery:

1. **Check Spam Folders**
   - Ask users to check their spam folders
   - Add instructions in your UI to whitelist your email domain

2. **Verify SPF and DKIM Records**
   - If using a custom domain, ensure proper SPF and DKIM records are set up
   - This significantly improves deliverability

3. **Monitor Delivery Rates**
   - Use the analytics provided by your email service to monitor delivery rates
   - Adjust your email content if you notice high spam rates

4. **Test with Different Email Providers**
   - Test your verification emails with Gmail, Outlook, Yahoo, etc.
   - Some providers have stricter spam filters than others

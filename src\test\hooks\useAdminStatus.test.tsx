import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useAdminStatus } from '@/hooks/use-admin-status';
import * as authHelpers from '@/utils/auth-helpers';

// Mock the auth helpers
vi.mock('@/utils/auth-helpers', () => ({
  isUserAdmin: vi.fn(),
}));

describe('useAdminStatus hook', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
  });

  it('returns isAdmin=false and isLoading=false when user is null', async () => {
    const { result } = renderHook(() => useAdminStatus(null));

    // Skip the initial loading check since it might be too fast
    // and already be false by the time we check

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isAdmin).toBe(false);
    expect(authHelpers.isUserAdmin).not.toHaveBeenCalled();
  });

  it('returns isAdmin=true when user is admin', async () => {
    // Mock the isUserAdmin function to return true
    vi.mocked(authHelpers.isUserAdmin).mockResolvedValue(true);

    const mockUser = { id: '123', email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useAdminStatus(mockUser));

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isAdmin).toBe(true);
    expect(authHelpers.isUserAdmin).toHaveBeenCalledWith(mockUser);
  });

  it('returns isAdmin=false when user is not admin', async () => {
    // Mock the isUserAdmin function to return false
    vi.mocked(authHelpers.isUserAdmin).mockResolvedValue(false);

    const mockUser = { id: '123', email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useAdminStatus(mockUser));

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isAdmin).toBe(false);
    expect(authHelpers.isUserAdmin).toHaveBeenCalledWith(mockUser);
  });

  it('handles errors by setting isAdmin to false', async () => {
    // Mock the isUserAdmin function to throw an error
    vi.mocked(authHelpers.isUserAdmin).mockRejectedValue(new Error('Test error'));

    // Spy on console.error to prevent error output in tests
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const mockUser = { id: '123', email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useAdminStatus(mockUser));

    // Initially loading
    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isAdmin).toBe(false);
    expect(authHelpers.isUserAdmin).toHaveBeenCalledWith(mockUser);
    expect(consoleErrorSpy).toHaveBeenCalled();

    // Restore console.error
    consoleErrorSpy.mockRestore();
  });
});

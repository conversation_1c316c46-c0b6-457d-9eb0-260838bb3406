import { useEffect, useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Navbar from '@/components/Navbar';
import BottomNavigation from '@/components/BottomNavigation';
import { Check, AlertCircle } from 'lucide-react';

const PaymentSuccess = () => {
  const location = useLocation();
  const [reference, setReference] = useState<string>('');
  const [planName, setPlanName] = useState<string>('');

  useEffect(() => {
    // Get query parameters from URL
    const params = new URLSearchParams(location.search);
    const ref = params.get('reference');
    const plan = params.get('plan');

    if (ref) setReference(ref);
    if (plan) setPlanName(plan);

    // In a real application, you would verify the payment on your backend here
  }, [location]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1 container mx-auto px-4 py-12">
        <motion.div
          className="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="p-8">
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <Check className="h-8 w-8 text-green-500" />
              </div>
            </div>

            <h1 className="text-2xl font-bold text-center text-gray-800 mb-4">
              Payment Successful!
            </h1>

            <p className="text-center text-gray-600 mb-6">
              Thank you for your payment. Your subscription to {planName || 'our service'} has been activated.
            </p>

            {reference && (
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <p className="text-sm text-gray-500">Transaction Reference:</p>
                <p className="font-mono text-gray-700">{reference}</p>
              </div>
            )}

            <div className="flex flex-col space-y-3">
              <Link
                to="/quizzes"
                className="py-3 px-4 bg-indigo-600 hover:bg-indigo-700 text-white text-center font-medium rounded-lg transition-colors"
              >
                Start Exploring Quizzes
              </Link>

              <Link
                to="/profile"
                className="py-3 px-4 bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium rounded-lg transition-colors"
              >
                View My Profile
              </Link>

              <div className="mt-6 p-4 bg-amber-50 rounded-lg border border-amber-200 flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-amber-800">
                  <p>Having issues with your payment or subscription?</p>
                  <Link to="/payment/troubleshoot" className="font-medium underline">View our troubleshooting guide</Link>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </main>

      <BottomNavigation />
    </div>
  );
};

export default PaymentSuccess;

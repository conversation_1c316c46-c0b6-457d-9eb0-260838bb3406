// <PERSON>ript to run the SQL migrations
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_SERVICE_KEY;

if (!supabaseKey) {
  console.error('Error: Supabase key not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runMigration() {
  try {
    console.log('Running migration to fix user_profiles and subscriptions tables...');

    // Read the migration file
    const migrationPath = path.join(__dirname, 'supabase/migrations/20240620000000_fix_user_profiles_and_subscriptions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the SQL
    const { error } = await supabase.rpc('execute_sql', { query: migrationSQL });

    if (error) {
      if (error.message.includes('function "execute_sql" does not exist')) {
        console.error('Error: The execute_sql function does not exist in your Supabase project.');
        console.log('Please run this SQL manually in the Supabase SQL editor.');
        console.log('Migration SQL:');
        console.log(migrationSQL);
      } else {
        console.error('Error executing migration:', error);
      }
      process.exit(1);
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();

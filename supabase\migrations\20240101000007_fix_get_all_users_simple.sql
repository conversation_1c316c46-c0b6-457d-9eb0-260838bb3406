-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.get_all_users_simple();

-- Create a new version of the function with the exact structure needed
CREATE OR REPLACE FUNCTION public.get_all_users_simple()
RETURNS TABLE (
  id uuid,
  email text,
  created_at timestamptz,
  last_sign_in_at timestamptz,
  is_subscribed boolean,
  is_admin boolean,
  subscription_expires_at timestamptz
)
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    au.id,
    au.email::text,
    au.created_at,
    au.last_sign_in_at,
    COALESCE((SELECT true FROM public.admin_users WHERE user_id = au.id), false) as is_admin,
    false as is_subscribed,
    NULL::timestamptz as subscription_expires_at
  FROM auth.users au
  ORDER BY au.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_all_users_simple() TO authenticated;

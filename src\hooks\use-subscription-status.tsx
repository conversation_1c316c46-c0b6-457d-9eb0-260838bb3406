import { useState, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { isUserSubscribed } from "@/utils/auth-helpers";

/**
 * Hook to check if a user has a premium subscription
 * Returns the subscription status and loading state
 */
export function useSubscriptionStatus(user: User | null) {
  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkSubscription = async () => {
      setIsLoading(true);
      try {
        if (!user) {
          setIsSubscribed(false);
          return;
        }

        const subscribed = await isUserSubscribed(user);
        setIsSubscribed(subscribed);
      } catch (error) {
        console.error("Error checking subscription status:", error);
        setIsSubscribed(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkSubscription();
  }, [user]);

  return { isSubscribed, isLoading };
}

import { Request, Response, NextFunction } from 'express';

// Error handling middleware
export default function errorHandler(err: any, req: Request, res: Response, next: NextFunction) {
  // Log the error
  console.error('Server Error:', {
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });

  // Determine status code
  const statusCode = err.statusCode || 500;

  // Send error response
  res.status(statusCode).json({
    status: 'error',
    message: err.message || 'An unexpected error occurred',
    error: process.env.NODE_ENV === 'production' ? undefined : err.stack
  });
}

// Not found middleware
export function notFoundHandler(req: Request, res: Response) {
  res.status(404).json({
    status: 'error',
    message: `Route not found: ${req.method} ${req.originalUrl}`
  });
}

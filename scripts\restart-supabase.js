// Script to restart Supabase
import { exec } from 'child_process';

console.log('Restarting Supabase...');

// Restart Supabase
exec('supabase stop && supabase start', (error, stdout, stderr) => {
  if (error) {
    console.error(`Error restarting Supabase: ${error.message}`);
    console.log('You may need to restart Supabase manually:');
    console.log('  supabase stop');
    console.log('  supabase start');
    process.exit(1);
  }
  
  if (stderr) {
    console.error(`Supabase restart stderr: ${stderr}`);
  }
  
  console.log(stdout);
  console.log('Supabase restarted successfully.');
  console.log('You can now test email delivery with:');
  console.log('  npm run test:email');
});

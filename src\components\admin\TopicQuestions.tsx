import { useState, useEffect } from "react";
import { useAdminQuestions, Question, Topic } from "@/hooks/use-admin";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  ChevronLeft,
  ChevronRight,
  Edit,
  Loader2,
  RefreshCw,
  Search,
  Trash2,
  X,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

// Utility function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};
import { supabase } from "@/integrations/supabase/client";

interface TopicQuestionsProps {
  topic: Topic;
  onBack: () => void;
  onEditQuestion: (question: Question) => void;
}

export function TopicQuestions({ topic, onBack, onEditQuestion }: TopicQuestionsProps) {
  const { questions, loading, error, refreshQuestions } = useAdminQuestions(topic.id);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  
  const itemsPerPage = 10;
  
  // Filter questions based on search term
  const filteredQuestions = questions.filter(
    (question) =>
      question.question_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (question.explanation && question.explanation.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Calculate pagination
  const totalPages = Math.ceil(filteredQuestions.length / itemsPerPage);
  const paginatedQuestions = filteredQuestions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  // Reset pagination when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);
  
  // Reset selected questions when topic changes
  useEffect(() => {
    setSelectedQuestions([]);
  }, [topic.id]);
  
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedQuestions(paginatedQuestions.map(q => q.id));
    } else {
      setSelectedQuestions([]);
    }
  };
  
  const handleSelectQuestion = (questionId: string, checked: boolean) => {
    if (checked) {
      setSelectedQuestions([...selectedQuestions, questionId]);
    } else {
      setSelectedQuestions(selectedQuestions.filter(id => id !== questionId));
    }
  };
  
  const handleDeleteSelected = async () => {
    if (selectedQuestions.length === 0) return;
    
    setIsDeleting(true);
    try {
      const { error } = await supabase
        .from("questions")
        .delete()
        .in("id", selectedQuestions);
      
      if (error) throw error;
      
      toast({
        title: "Questions deleted",
        description: `Successfully deleted ${selectedQuestions.length} questions.`,
      });
      
      setSelectedQuestions([]);
      refreshQuestions();
    } catch (err) {
      console.error("Error deleting questions:", err);
      toast({
        title: "Error deleting questions",
        description: err.message,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setConfirmDelete(false);
    }
  };
  
  const handleDeleteQuestion = async (questionId: string) => {
    try {
      const { error } = await supabase
        .from("questions")
        .delete()
        .eq("id", questionId);
      
      if (error) throw error;
      
      toast({
        title: "Question deleted",
        description: "Question has been successfully deleted.",
      });
      
      refreshQuestions();
    } catch (err) {
      console.error("Error deleting question:", err);
      toast({
        title: "Error deleting question",
        description: err.message,
        variant: "destructive",
      });
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={onBack}>
            <ChevronLeft className="h-4 w-4 mr-1" /> Back to Topics
          </Button>
          <h2 className="text-xl font-semibold">{topic.title} Questions</h2>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshQuestions}
          disabled={loading}
        >
          <RefreshCw className="h-4 w-4 mr-1" /> Refresh
        </Button>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search questions..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-0 h-full"
              onClick={() => setSearchTerm("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        {selectedQuestions.length > 0 && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setConfirmDelete(true)}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <Loader2 className="h-4 w-4 mr-1 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4 mr-1" />
            )}
            Delete Selected ({selectedQuestions.length})
          </Button>
        )}
      </div>
      
      {loading ? (
        <div className="p-4 text-center">
          <Loader2 className="h-8 w-8 text-cyber-primary animate-spin mx-auto mb-2" />
          <p>Loading questions...</p>
        </div>
      ) : error ? (
        <div className="p-4 text-center">
          <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-lg mx-auto">
            <h3 className="text-red-800 font-medium mb-2">Error Loading Questions</h3>
            <p className="text-red-700 text-sm mb-3">{error}</p>
            <Button
              variant="outline"
              className="mt-3"
              onClick={refreshQuestions}
            >
              <RefreshCw className="h-4 w-4 mr-2" /> Try Again
            </Button>
          </div>
        </div>
      ) : (
        <div className="border rounded-md overflow-hidden">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="p-3 w-10">
                  <Checkbox
                    checked={
                      paginatedQuestions.length > 0 &&
                      paginatedQuestions.every(q => selectedQuestions.includes(q.id))
                    }
                    onCheckedChange={handleSelectAll}
                  />
                </th>
                <th className="text-left font-medium p-3">Question</th>
                <th className="text-left font-medium p-3 w-32">Difficulty</th>
                <th className="text-left font-medium p-3 w-24">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y">
              {paginatedQuestions.length > 0 ? (
                paginatedQuestions.map((question) => (
                  <tr key={question.id} className="hover:bg-muted/20">
                    <td className="p-3">
                      <Checkbox
                        checked={selectedQuestions.includes(question.id)}
                        onCheckedChange={(checked) => 
                          handleSelectQuestion(question.id, checked as boolean)
                        }
                      />
                    </td>
                    <td className="p-3">
                      <p className="text-sm line-clamp-2">{decodeHtmlEntities(question.question_text)}</p>
                    </td>
                    <td className="p-3">
                      <div className={`px-2 py-0.5 rounded-full text-xs font-medium text-center
                        ${question.difficulty === "easy" ? "bg-green-100 text-green-700" :
                          question.difficulty === "medium" ? "bg-yellow-100 text-yellow-700" :
                          "bg-red-100 text-red-700"}`}>
                        {question.difficulty}
                      </div>
                    </td>
                    <td className="p-3">
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => onEditQuestion(question)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteQuestion(question.id)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="p-4 text-center text-muted-foreground">
                    {searchTerm
                      ? "No questions found matching your search."
                      : "No questions found for this topic."}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          
          {totalPages > 1 && (
            <div className="flex items-center justify-between px-4 py-3 border-t">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredQuestions.length)} of {filteredQuestions.length} questions
              </div>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
      
      <AlertDialog open={confirmDelete} onOpenChange={setConfirmDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete {selectedQuestions.length} selected questions.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSelected}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>Delete</>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

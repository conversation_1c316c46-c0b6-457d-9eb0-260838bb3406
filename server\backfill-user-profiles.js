// backfill-user-profiles.js
// Script to backfill user_profiles and profiles tables from Supabase Auth users

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Role Key.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function main() {
  // Fetch all users from Supabase Auth
  const { data: userData, error: userError } = await supabase.auth.admin.listUsers();
  if (userError) {
    console.error('Error fetching users:', userError);
    process.exit(1);
  }
  const users = userData.users;
  let userProfilesInserted = 0;
  let profilesInserted = 0;
  for (const user of users) {
    // Backfill user_profiles
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', user.id)
      .maybeSingle();
    if (!existingProfile) {
      const { error: insertError } = await supabase
        .from('user_profiles')
        .insert({ id: user.id, email: user.email });
      if (insertError) {
        console.error(`Error inserting user_profiles for ${user.email}:`, insertError);
      } else {
        userProfilesInserted++;
      }
    }
    // Backfill profiles
    const { data: existingLegacy } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', user.id)
      .maybeSingle();
    if (!existingLegacy) {
      const { error: insertLegacyError } = await supabase
        .from('profiles')
        .insert({ id: user.id, full_name: user.user_metadata?.full_name || null, is_admin: false, subscription_status: 'free' });
      if (insertLegacyError) {
        console.error(`Error inserting profiles for ${user.email}:`, insertLegacyError);
      } else {
        profilesInserted++;
      }
    }
  }
  console.log(`Backfill complete. Inserted ${userProfilesInserted} into user_profiles, ${profilesInserted} into profiles.`);
}

main().catch((err) => {
  console.error('Fatal error:', err);
  process.exit(1);
}); 
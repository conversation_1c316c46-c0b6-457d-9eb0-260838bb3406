// <PERSON><PERSON>t to fix the specific ISC2 Certification quiz questions shown in the screenshots
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// The specific questions and their correct answers
const specificQuestions = [
  {
    text: "A security engineer discovers that an employee has been accessing sensitive customer data outside of business hours with no clear business justification. Which principle best describes the concern in this scenario?",
    correctAnswer: "B" // B: Need to know
  },
  {
    text: "An organization implements multi-factor authentication requiring users to present something they know, something they have, and something they are. What type of authentication model is this?",
    correctAnswer: "B" // B: Three-factor authentication
  },
  {
    text: "A security professional is implementing a security control that will remain effective even if attackers gain full knowledge of its design and implementation. Which security principle is being followed?",
    correctAnswer: "C" // C: Kerckhoffs' principle
  }
];

// Function to find and fix the specific questions
async function fixSpecificQuestions() {
  try {
    console.log('Starting to fix specific ISC2 Certification quiz questions...');

    // Find the ISC2 Certification topic
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('id, title')
      .or('title.eq.ISC2 Certification,title.eq.CPN Cybersecurity Certification Exam');

    if (topicsError) {
      console.error('Error fetching topics:', topicsError);
      return;
    }

    if (!topics || topics.length === 0) {
      console.error('Could not find ISC2 Certification topic');
      return;
    }

    console.log(`Found ${topics.length} relevant topics:`);
    topics.forEach(topic => console.log(`- ${topic.title} (${topic.id})`));

    // Process each topic
    for (const topic of topics) {
      console.log(`\nProcessing topic: ${topic.title}`);

      // Get all questions for this topic
      const { data: questions, error: questionsError } = await supabase
        .from('questions')
        .select('*')
        .eq('topic_id', topic.id);

      if (questionsError) {
        console.error(`Error fetching questions for topic ${topic.title}:`, questionsError);
        continue;
      }

      console.log(`Found ${questions.length} questions for topic ${topic.title}`);

      // Find and fix the specific questions
      const questionsToFix = [];

      for (const specificQuestion of specificQuestions) {
        // Find the question in the database
        const matchingQuestion = questions.find(q =>
          q.question_text.includes(specificQuestion.text) ||
          specificQuestion.text.includes(q.question_text)
        );

        if (matchingQuestion) {
          // Check if the answer is already correct
          if (matchingQuestion.correct_answer === specificQuestion.correctAnswer) {
            console.log(`Question "${matchingQuestion.question_text.substring(0, 50)}..." already has the correct answer.`);
            continue;
          }

          questionsToFix.push({
            id: matchingQuestion.id,
            text: matchingQuestion.question_text,
            currentAnswer: matchingQuestion.correct_answer,
            newAnswer: specificQuestion.correctAnswer
          });
        } else {
          console.log(`Could not find question: "${specificQuestion.text.substring(0, 50)}..."`);
        }
      }

      console.log(`\nFound ${questionsToFix.length} questions to fix for topic ${topic.title}:`);

      // Display questions that will be fixed
      for (const q of questionsToFix) {
        console.log(`\nQuestion: ${q.text.substring(0, 100)}...`);
        console.log(`Current answer: ${q.currentAnswer}`);
        console.log(`New answer: ${q.newAnswer}`);
      }

      // Fix the questions
      if (questionsToFix.length > 0) {
        console.log(`\nFixing ${questionsToFix.length} questions for topic ${topic.title}...`);

        let successCount = 0;

        for (const q of questionsToFix) {
          const { error } = await supabase
            .from('questions')
            .update({ correct_answer: q.newAnswer })
            .eq('id', q.id);

          if (error) {
            console.error(`Error updating question ${q.id}:`, error);
          } else {
            successCount++;
            console.log(`Updated question: ${q.id}`);
          }
        }

        console.log(`\nUpdate complete for topic ${topic.title}. ${successCount}/${questionsToFix.length} questions updated successfully.`);
      }
    }

    console.log('\nAll topics processed.');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixSpecificQuestions();

// <PERSON>ript to directly query NDPR questions
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function queryQuestions() {
  try {
    // Query the DPIA question
    const dpiaId = 'b818fc87-79df-47a7-9b6e-8472dbeedaec';
    
    const { data: dpiaQuestion, error: dpiaError } = await supabase
      .from('questions')
      .select('*')
      .eq('id', dpiaId)
      .single();
    
    if (dpiaError) {
      console.error('Error querying DPIA question:', dpiaError);
    } else {
      console.log('DPIA Question:');
      console.log(JSON.stringify(dpiaQuestion, null, 2));
    }
    
    // Query the DPO question
    const dpoId = 'a15ded3b-0edb-4f8d-9dda-d44687f36119';
    
    const { data: dpoQuestion, error: dpoError } = await supabase
      .from('questions')
      .select('*')
      .eq('id', dpoId)
      .single();
    
    if (dpoError) {
      console.error('Error querying DPO question:', dpoError);
    } else {
      console.log('\nDPO Question:');
      console.log(JSON.stringify(dpoQuestion, null, 2));
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
queryQuestions();

// Script to check database schema
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from parent directory
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSchema() {
  try {
    console.log('Checking learning_materials table schema...');
    
    // Get schema information for learning_materials table
    const { data: columns, error } = await supabase
      .from('learning_materials')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('Error fetching learning_materials:', error.message);
      return;
    }
    
    if (!columns || columns.length === 0) {
      console.log('No data found in learning_materials table');
      return;
    }
    
    // Display column names and types
    console.log('learning_materials columns:');
    const sampleRow = columns[0];
    Object.keys(sampleRow).forEach(column => {
      const value = sampleRow[column];
      const type = typeof value;
      console.log(`- ${column}: ${type} ${value === null ? '(null)' : ''}`);
    });
    
    // Check for any large content fields
    if (sampleRow.content) {
      console.log(`Content length: ${sampleRow.content.length} characters`);
    }
    
    // Check topics table
    console.log('\nChecking topics table...');
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('*')
      .limit(1);
    
    if (topicsError) {
      console.error('Error fetching topics:', topicsError.message);
      return;
    }
    
    if (!topics || topics.length === 0) {
      console.log('No data found in topics table');
      return;
    }
    
    console.log('topics columns:');
    const topicRow = topics[0];
    Object.keys(topicRow).forEach(column => {
      const value = topicRow[column];
      const type = typeof value;
      console.log(`- ${column}: ${type} ${value === null ? '(null)' : ''}`);
    });
    
    // Count total learning materials
    const { count: materialCount, error: countError } = await supabase
      .from('learning_materials')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error counting learning materials:', countError.message);
    } else {
      console.log(`\nTotal learning materials: ${materialCount}`);
    }
    
    // Count total topics
    const { count: topicCount, error: topicCountError } = await supabase
      .from('topics')
      .select('*', { count: 'exact', head: true });
    
    if (topicCountError) {
      console.error('Error counting topics:', topicCountError.message);
    } else {
      console.log(`Total topics: ${topicCount}`);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

checkSchema();

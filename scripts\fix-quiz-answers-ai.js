import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for CISSP Fundamentals
const CISSP_TOPIC_ID = '1de31e9d-97e5-4ee7-9e89-968615011645';

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);

  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }

  return data;
}

async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

function analyzeQuestion(question) {
  const { id, question_text, options, correct_answer, explanation } = question;

  // Convert options to array for easier analysis
  let optionsArray = [];
  let optionKeys = [];

  if (typeof options === 'object') {
    // Handle both numeric and letter keys
    if ('A' in options || 'B' in options) {
      // Letter keys (A, B, C, D)
      optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
    } else {
      // Numeric keys (0, 1, 2, 3)
      optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
    }

    optionsArray = optionKeys.map(key => ({
      key,
      text: options[key]
    }));
  }

  // Get current answer text
  const currentOptionText = options[correct_answer];

  // Initialize result
  const result = {
    id,
    question_text,
    options,
    option_keys: optionKeys,
    current_answer: correct_answer,
    current_answer_text: currentOptionText,
    explanation,
    needs_review: false,
    suggested_answer: null,
    suggested_answer_text: null,
    confidence: 'high',
    reasoning: ''
  };

  // Advanced analysis based on explanation and question

  // 1. Check if explanation directly mentions the correct answer
  const explanationLower = explanation.toLowerCase();

  // For each option, check if it's strongly indicated in the explanation
  let bestMatchKey = null;
  let bestMatchScore = 0;
  let bestMatchReasoning = '';

  for (const key of optionKeys) {
    const optionText = options[key];
    if (!optionText) continue;

    const optionLower = optionText.toLowerCase();
    let matchScore = 0;
    let matchReasons = [];

    // Check for direct mention of the option text in the explanation
    if (explanationLower.includes(optionLower)) {
      matchScore += 3;
      matchReasons.push(`Option text "${optionText}" appears directly in the explanation`);
    }

    // Check for key phrases that indicate this is the correct answer
    const correctPhrases = [
      'correct answer', 'right answer', 'is correct', 'is right',
      'refers to', 'is defined as', 'is the', 'involves', 'means'
    ];

    for (const phrase of correctPhrases) {
      if (explanationLower.includes(`${phrase} ${optionLower}`) ||
          explanationLower.includes(`${optionLower} ${phrase}`)) {
        matchScore += 2;
        matchReasons.push(`Explanation contains phrase indicating this is correct: "${phrase}"`);
      }
    }

    // Check for key words from the option in the explanation
    const optionWords = optionLower.split(/\s+/).filter(word => word.length > 3);
    for (const word of optionWords) {
      if (explanationLower.includes(word)) {
        matchScore += 0.5;
        matchReasons.push(`Key word "${word}" from option appears in explanation`);
      }
    }

    // Special case: If explanation starts by repeating the option text
    if (explanationLower.startsWith(optionLower) ||
        explanationLower.startsWith(optionLower.replace(/^(a|an|the) /, ''))) {
      matchScore += 2;
      matchReasons.push('Explanation begins with the option text');
    }

    // If this option has a better match than our current best
    if (matchScore > bestMatchScore) {
      bestMatchScore = matchScore;
      bestMatchKey = key;
      bestMatchReasoning = matchReasons.join('. ');
    }
  }

  // Determine confidence level based on match score
  let confidence = 'low';
  if (bestMatchScore > 4) confidence = 'high';
  else if (bestMatchScore > 2) confidence = 'medium';

  // If we found a better match and it's different from current answer
  if (bestMatchKey && bestMatchKey !== correct_answer && bestMatchScore > 1) {
    result.needs_review = true;
    result.suggested_answer = bestMatchKey;
    result.suggested_answer_text = options[bestMatchKey];
    result.confidence = confidence;
    result.reasoning = bestMatchReasoning;
  }

  return result;
}

async function main() {
  try {
    console.log('Fetching CISSP Fundamentals questions...');
    const questions = await getQuestionsForTopic(CISSP_TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);

    const analysisResults = questions.map(analyzeQuestion);
    const questionsNeedingReview = analysisResults.filter(result => result.needs_review);

    console.log(`\n${questionsNeedingReview.length} questions need review.`);

    for (const result of questionsNeedingReview) {
      console.log('\n' + '-'.repeat(80));
      console.log(`QUESTION: ${result.question_text}`);

      // Display all options
      console.log('\nOPTIONS:');
      for (const key of result.option_keys) {
        const isCurrentAnswer = key === result.current_answer;
        const isSuggestedAnswer = key === result.suggested_answer;
        const marker = isCurrentAnswer ? '(Current)' : isSuggestedAnswer ? '(Suggested)' : '';
        console.log(`  ${key}: ${result.options[key]} ${marker}`);
      }

      console.log(`\nCURRENT ANSWER: ${result.current_answer} - "${result.current_answer_text}"`);
      console.log(`SUGGESTED ANSWER: ${result.suggested_answer} - "${result.suggested_answer_text}"`);
      console.log(`EXPLANATION: ${result.explanation}`);
      console.log(`CONFIDENCE: ${result.confidence}`);
      console.log(`REASONING: ${result.reasoning}`);

      // Ask for confirmation before updating
      const readline = createInterface({
        input: process.stdin,
        output: process.stdout
      });

      const answer = await new Promise(resolve => {
        readline.question('Update this question? (y/n/skip/custom): ', resolve);
      });

      if (answer.toLowerCase() === 'y') {
        const success = await updateQuestionCorrectAnswer(result.id, result.suggested_answer);
        console.log(success ? 'Updated successfully!' : 'Update failed.');
      } else if (answer.toLowerCase() === 'skip') {
        console.log('Skipping remaining questions.');
        readline.close();
        break;
      } else if (answer.toLowerCase() === 'custom') {
        const customAnswer = await new Promise(resolve => {
          readline.question('Enter custom answer key (e.g., A, B, 0, 1): ', resolve);
        });

        if (result.option_keys.includes(customAnswer)) {
          const success = await updateQuestionCorrectAnswer(result.id, customAnswer);
          console.log(success ? 'Updated successfully with custom answer!' : 'Update failed.');
        } else {
          console.log(`Invalid answer key. Must be one of: ${result.option_keys.join(', ')}`);
        }
      } else {
        console.log('Skipped.');
      }

      readline.close();
    }

    console.log('\nAnalysis complete!');

  } catch (error) {
    console.error('Error:', error);
  }
}

main();

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import BottomNavigation from '@/components/BottomNavigation';
import * as useAuthModule from '@/hooks/use-auth';
import * as useAdminStatusModule from '@/hooks/use-admin-status';
import * as useMobileModule from '@/hooks/use-mobile';
// Create a mock BottomNavigation component for testing
vi.mock('@/components/BottomNavigation', () => ({
  default: () => {
    const { useAuth } = useAuthModule;
    const { useAdminStatus } = useAdminStatusModule;
    const { useIsMobile } = useMobileModule;

    const isMobile = useIsMobile();
    const { user } = useAuth();
    const { isAdmin } = useAdminStatus(user);

    // Hide bottom navigation on desktop
    if (!isMobile) return null;

    // Define base navigation items
    const baseNavItems = [
      { name: 'Home', path: '/' },
      { name: 'Quizzes', path: '/quizzes' }
    ];

    // Auth-dependent items
    const authItems = user ? [
      { name: 'Profile', path: '/profile' }
    ] : [
      { name: 'Sign In', path: '/auth' }
    ];

    // Admin items
    const adminItems = isAdmin ? [
      { name: 'Admin', path: '/admin' }
    ] : [];

    // Combine all items
    const displayItems = [...baseNavItems, ...authItems, ...adminItems];

    return (
      <div data-testid="bottom-navigation">
        {displayItems.map((item) => (
          <a key={item.name} href={item.path}>{item.name}</a>
        ))}
      </div>
    );
  },
}));

// Mock the hooks
vi.mock('@/hooks/use-auth', async () => {
  const actual = await vi.importActual('@/hooks/use-auth');
  return {
    ...actual,
    useAuth: vi.fn(),
  };
});

vi.mock('@/hooks/use-admin-status', async () => {
  const actual = await vi.importActual('@/hooks/use-admin-status');
  return {
    ...actual,
    useAdminStatus: vi.fn(),
  };
});

vi.mock('@/hooks/use-mobile', async () => {
  const actual = await vi.importActual('@/hooks/use-mobile');
  return {
    ...actual,
    useIsMobile: vi.fn(),
  };
});

describe('BottomNavigation component', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: null,
      session: null,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    vi.mocked(useAdminStatusModule.useAdminStatus).mockReturnValue({
      isAdmin: false,
      isLoading: false,
    });

    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);
  });

  it('does not render on desktop', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(false);

    const { container } = render(<BottomNavigation />);

    // Should not render anything
    expect(container.firstChild).toBeNull();
  });

  it('renders on mobile with basic navigation items', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);

    render(<BottomNavigation />);

    // Check for basic navigation items
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Quizzes')).toBeInTheDocument();
    expect(screen.getByText('Sign In')).toBeInTheDocument();
  });

  it('shows Profile link when user is logged in', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: { id: '123', email: '<EMAIL>' } as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    render(<BottomNavigation />);

    // Should show Profile link
    expect(screen.getByText('Profile')).toBeInTheDocument();
    // Should not show Sign In link
    expect(screen.queryByText('Sign In')).not.toBeInTheDocument();
  });

  it('shows Admin link when user is admin', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: { id: '123', email: '<EMAIL>' } as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });
    vi.mocked(useAdminStatusModule.useAdminStatus).mockReturnValue({
      isAdmin: true,
      isLoading: false,
    });

    render(<BottomNavigation />);

    // Should show Admin link
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });

  it('does not show Admin link when user is not admin', () => {
    vi.mocked(useMobileModule.useIsMobile).mockReturnValue(true);
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: { id: '123', email: '<EMAIL>' } as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });
    vi.mocked(useAdminStatusModule.useAdminStatus).mockReturnValue({
      isAdmin: false,
      isLoading: false,
    });

    render(<BottomNavigation />);

    // Should not show Admin link
    expect(screen.queryByText('Admin')).not.toBeInTheDocument();
  });
});

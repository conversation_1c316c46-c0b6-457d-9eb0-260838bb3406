// Script to clean up learning material content by extracting body content
// Run with: node scripts/clean-content.js

import { createClient } from '@supabase/supabase-js';
import { JSDOM } from 'jsdom';
import DOMPurify from 'dompurify';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env file
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.resolve(__dirname, '../.env');
const envContent = fs.readFileSync(envPath, 'utf8');

// Parse environment variables
const env = {};
envContent.split('\n').forEach(line => {
  const match = line.match(/^([^=]+)=(.*)$/);
  if (match) {
    const key = match[1].trim();
    const value = match[2].trim();
    env[key] = value;
  }
});

// Initialize Supabase client
const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
console.log('Supabase URL:', supabaseUrl);
const supabase = createClient(supabaseUrl, supabaseKey);

// Create a DOM window for DOMPurify
const window = new JSDOM('').window;
const purify = DOMPurify(window);

/**
 * Extracts and cleans body content from HTML
 * @param {string} htmlContent - The full HTML content
 * @returns {string} - The cleaned body content
 */
function cleanHtmlContent(htmlContent) {
  try {
    // If not a full HTML document, just sanitize and return
    if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html')) {
      return purify.sanitize(htmlContent, {
        ALLOWED_TAGS: [
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'hr',
          'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
          'em', 'strong', 'del', 'a', 'img', 'table', 'thead', 'tbody',
          'tr', 'th', 'td', 'div', 'span', 'section', 'article'
        ],
        ALLOWED_ATTR: ['href', 'target', 'class', 'id', 'style']
      });
    }

    // Parse the HTML
    const dom = new JSDOM(htmlContent);
    const document = dom.window.document;

    // Extract body content
    let bodyContent = document.body.innerHTML;

    // Clean the content
    bodyContent = purify.sanitize(bodyContent, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'br', 'hr',
        'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
        'em', 'strong', 'del', 'a', 'img', 'table', 'thead', 'tbody',
        'tr', 'th', 'td', 'div', 'span', 'section', 'article'
      ],
      ALLOWED_ATTR: ['href', 'target', 'class', 'id', 'style']
    });

    return bodyContent;
  } catch (error) {
    console.error('Error cleaning HTML content:', error);
    return htmlContent; // Return original content if cleaning fails
  }
}

async function cleanLearningMaterials() {
  try {
    console.log('Cleaning learning materials content...');

    // Fetch all learning materials
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*');

    if (error) {
      console.error('Error fetching learning materials:', error);
      return;
    }

    console.log(`Found ${data.length} learning materials`);

    // Process each material
    for (const material of data) {
      console.log(`\nProcessing material: ${material.title}`);

      // Skip if no content
      if (!material.content) {
        console.log('No content found for this material');
        continue;
      }

      // Clean the content
      const originalLength = material.content.length;
      const cleanedContent = cleanHtmlContent(material.content);
      const newLength = cleanedContent.length;

      // Update the material with cleaned content
      const { error: updateError } = await supabase
        .from('learning_materials')
        .update({ content: cleanedContent })
        .eq('id', material.id);

      if (updateError) {
        console.error(`Error updating material ${material.id}:`, updateError);
      } else {
        console.log(`Successfully updated material: ${material.title}`);
        console.log(`Original content length: ${originalLength}`);
        console.log(`New content length: ${newLength}`);
        console.log(`Reduction: ${originalLength - newLength} characters (${Math.round((originalLength - newLength) / originalLength * 100)}%)`);
      }
    }

    console.log('\nContent cleaning complete');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
cleanLearningMaterials();

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Feedback Local Storage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Feedback Local Storage Test</h1>
        <p>This page helps test the feedback local storage functionality.</p>
        
        <div id="output"></div>
        
        <button onclick="addTestFeedback()">Add Test Feedback to Local Storage</button>
        <button onclick="viewStoredFeedback()">View Stored Feedback</button>
        <button onclick="clearFeedback()">Clear All Feedback</button>
        <button onclick="openAdminDashboard()">Open Admin Dashboard</button>
        
        <h2>Instructions:</h2>
        <ol>
            <li>Click "Add Test Feedback to Local Storage" to create test feedback items</li>
            <li>Click "Open Admin Dashboard" to go to the admin dashboard</li>
            <li>Navigate to the "Feedback" tab in the admin dashboard</li>
            <li>You should see the test feedback items displayed</li>
        </ol>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = message;
            output.appendChild(div);
            console.log(message);
        }

        function addTestFeedback() {
            log('Adding test feedback to localStorage...', 'info');
            
            const testFeedback = [
                {
                    id: 'test-' + Date.now() + '-1',
                    name: 'John Doe',
                    email: '<EMAIL>',
                    subject: 'Test Feedback 1',
                    message: 'This is a test feedback message to verify the local storage functionality works correctly.',
                    user_id: null,
                    status: 'new',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                },
                {
                    id: 'test-' + Date.now() + '-2',
                    name: 'Jane Smith',
                    email: '<EMAIL>',
                    subject: 'Feature Request',
                    message: 'I would like to request a new feature for the quiz application. More detailed explanations would be helpful.',
                    user_id: null,
                    status: 'new',
                    created_at: new Date(Date.now() - 60000).toISOString(),
                    updated_at: new Date(Date.now() - 60000).toISOString()
                },
                {
                    id: 'test-' + Date.now() + '-3',
                    name: 'Bob Johnson',
                    email: '<EMAIL>',
                    subject: 'Bug Report',
                    message: 'I found a bug in the quiz system. When I submit an answer, sometimes it doesn\'t register correctly.',
                    user_id: null,
                    status: 'new',
                    created_at: new Date(Date.now() - 120000).toISOString(),
                    updated_at: new Date(Date.now() - 120000).toISOString()
                }
            ];
            
            try {
                // Get existing feedback
                const existing = localStorage.getItem('pendingFeedback');
                const existingFeedback = existing ? JSON.parse(existing) : [];
                
                // Add new test feedback
                const combined = [...existingFeedback, ...testFeedback];
                
                localStorage.setItem('pendingFeedback', JSON.stringify(combined));
                
                log('✅ Successfully added ' + testFeedback.length + ' test feedback items', 'success');
                log('📊 Total feedback items in storage: ' + combined.length, 'info');
                
            } catch (error) {
                log('❌ Error adding test feedback: ' + error.message, 'error');
            }
        }

        function viewStoredFeedback() {
            log('Viewing stored feedback...', 'info');
            
            try {
                const stored = localStorage.getItem('pendingFeedback');
                if (stored) {
                    const feedback = JSON.parse(stored);
                    log('📋 Found ' + feedback.length + ' feedback items:', 'success');
                    
                    feedback.forEach((item, index) => {
                        log(`${index + 1}. ${item.name} (${item.email}) - ${item.subject}`, 'info');
                    });
                    
                    log('<pre>' + JSON.stringify(feedback, null, 2) + '</pre>', 'info');
                } else {
                    log('📭 No feedback found in localStorage', 'info');
                }
            } catch (error) {
                log('❌ Error viewing feedback: ' + error.message, 'error');
            }
        }

        function clearFeedback() {
            try {
                localStorage.removeItem('pendingFeedback');
                log('🗑️ All feedback cleared from localStorage', 'success');
            } catch (error) {
                log('❌ Error clearing feedback: ' + error.message, 'error');
            }
        }

        function openAdminDashboard() {
            window.open('http://localhost:8082/admin', '_blank');
        }

        // Auto-load current status
        window.onload = function() {
            log('🔍 Checking current localStorage status...', 'info');
            viewStoredFeedback();
        };
    </script>
</body>
</html>

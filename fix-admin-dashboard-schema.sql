-- Fix Admin Dashboard Schema Issues
-- This script recreates the user_profiles table and fixes all related issues

-- Step 1: Create the user_profiles table with the correct schema
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT DEFAULT 'free',
  subscription_plan TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Step 2: Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Step 3: Create RLS policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
CREATE POLICY "Users can update their own profile"
  ON public.user_profiles
  FOR UPDATE
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
CREATE POLICY "Admins can view all profiles"
  ON public.user_profiles
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

DROP POLICY IF EXISTS "Admins can update all profiles" ON public.user_profiles;
CREATE POLICY "Admins can update all profiles"
  ON public.user_profiles
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

DROP POLICY IF EXISTS "Admins can insert profiles" ON public.user_profiles;
CREATE POLICY "Admins can insert profiles"
  ON public.user_profiles
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.user_profiles
      WHERE user_id = auth.uid() AND is_admin = true
    )
  );

-- Step 4: Create triggers for automatic updates
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON public.user_profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_updated_at();

-- Step 5: Create trigger for new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into user_profiles with email
  INSERT INTO public.user_profiles (user_id, email, is_subscribed, is_admin)
  VALUES (NEW.id, NEW.email, false, false)
  ON CONFLICT (user_id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 6: Create email sync trigger
CREATE OR REPLACE FUNCTION public.sync_user_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the email in user_profiles when it changes in auth.users
  UPDATE public.user_profiles
  SET email = NEW.email, updated_at = now()
  WHERE user_id = NEW.id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS sync_user_email_trigger ON auth.users;
CREATE TRIGGER sync_user_email_trigger
  AFTER UPDATE OF email ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_user_email();

-- Step 7: Backfill existing users with profiles
INSERT INTO public.user_profiles (user_id, email, full_name, is_subscribed, is_admin)
SELECT 
  au.id as user_id,
  au.email,
  au.raw_user_meta_data->>'full_name' as full_name,
  false as is_subscribed,
  false as is_admin
FROM auth.users au
WHERE au.id NOT IN (SELECT user_id FROM public.user_profiles)
ON CONFLICT (user_id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = EXCLUDED.full_name,
  updated_at = now();

-- Step 8: Migrate data from profiles table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'profiles') THEN
    -- Update user_profiles with data from profiles table
    UPDATE public.user_profiles
    SET 
      full_name = COALESCE(user_profiles.full_name, p.full_name),
      is_admin = COALESCE(p.is_admin, user_profiles.is_admin),
      subscription_expires_at = COALESCE(p.subscription_ends_at, user_profiles.subscription_expires_at),
      subscription_status = COALESCE(p.subscription_status, user_profiles.subscription_status),
      updated_at = now()
    FROM public.profiles p
    WHERE user_profiles.user_id = p.id;
  END IF;
END $$;

-- Step 9: Grant admin privileges to hardcoded admin emails
UPDATE public.user_profiles
SET is_admin = true, updated_at = now()
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

-- Step 10: Grant premium access to specific users
UPDATE public.user_profiles
SET 
  is_subscribed = true,
  subscription_expires_at = now() + interval '1 year',
  subscription_status = 'active',
  subscription_plan = 'elite',
  updated_at = now()
WHERE user_id IN (
  SELECT id FROM auth.users
  WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>')
);

-- Step 11: Create the get_all_user_profiles RPC function
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Return all user profiles with auth data
  RETURN QUERY
  SELECT
    up.id,
    up.user_id,
    up.email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at,
    au.last_sign_in_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  ORDER BY up.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 12: Create the is_admin function
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user_profiles table exists and has is_admin column
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'user_profiles' 
    AND column_name = 'is_admin'
  ) THEN
    -- Return true if the current user has is_admin = true in user_profiles
    RETURN EXISTS (
      SELECT 1 FROM public.user_profiles 
      WHERE user_id = auth.uid() AND is_admin = true
    );
  END IF;

  -- Default to false if table doesn't exist or user is not admin
  RETURN false;
END;
$$ LANGUAGE plpgsql;

-- Step 13: Create delete_user function
CREATE OR REPLACE FUNCTION public.delete_user(user_id UUID)
RETURNS JSON
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  -- Delete user and related data
  DELETE FROM public.user_profiles WHERE user_profiles.user_id = delete_user.user_id;
  DELETE FROM public.subscriptions WHERE subscriptions.user_id = delete_user.user_id;
  DELETE FROM public.payments WHERE payments.user_id = delete_user.user_id;
  DELETE FROM public.quiz_attempts WHERE quiz_attempts.user_id = delete_user.user_id;
  DELETE FROM public.feedback WHERE feedback.user_id = delete_user.user_id;
  
  -- Note: We don't delete from auth.users as that requires special permissions
  
  result := json_build_object('success', true, 'message', 'User data deleted successfully');
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_all_user_profiles() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.delete_user(UUID) TO authenticated;

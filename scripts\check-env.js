// This script checks if .env file exists and creates it if needed
// Run with: node scripts/check-env.js

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const envPath = path.join(rootDir, '.env');
const envExamplePath = path.join(rootDir, '.env.example');

// Check if .env file exists
if (!fs.existsSync(envPath)) {
  console.log('.env file not found. Creating from .env.example...');

  // Check if .env.example exists
  if (fs.existsSync(envExamplePath)) {
    // Copy .env.example to .env
    fs.copyFileSync(envExamplePath, envPath);
    console.log('.env file created successfully!');
    console.log('Please update the Supabase credentials in the .env file:');
    console.log('- VITE_SUPABASE_URL=your_supabase_url_here');
    console.log('- VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here');
  } else {
    console.log('.env.example file not found. Creating a basic .env file...');

    // Create a basic .env file
    const basicEnvContent = `# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Supabase Service Role Key (for admin scripts only, never expose in browser)
# Get this from Project Settings > API > service_role key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Application Settings
VITE_APP_NAME=SecQuiz
VITE_APP_DESCRIPTION=A cybersecurity education platform with interactive quizzes
VITE_APP_URL=http://localhost:5173

# Feature Flags
VITE_ENABLE_ADMIN_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG_MODE=true
`;

    fs.writeFileSync(envPath, basicEnvContent);
    console.log('Basic .env file created successfully!');
    console.log('Please update the Supabase credentials in the .env file.');
  }
} else {
  console.log('.env file already exists.');

  // Read the .env file
  const envContent = fs.readFileSync(envPath, 'utf8');

  // Check if Supabase credentials are set
  if (envContent.includes('VITE_SUPABASE_URL=your_supabase_url_here') ||
      envContent.includes('VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here') ||
      envContent.includes('SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here')) {
    console.log('WARNING: Supabase credentials in .env file need to be updated!');
    console.log('Please update the following in your .env file:');
    console.log('- VITE_SUPABASE_URL=your_supabase_url_here');
    console.log('- VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here');
    console.log('- SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here (for admin scripts)');
  } else {
    console.log('Supabase credentials appear to be set in .env file.');
  }
}

console.log('\nTo run the import script:');
console.log('1. Make sure your Supabase credentials are correctly set in the .env file');
console.log('2. Run: node scripts/import-quiz-data.js');

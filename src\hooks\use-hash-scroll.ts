import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * A custom hook that scrolls to the element matching the hash in the URL
 * when the component mounts or when the location changes.
 */
export function useHashScroll() {
  const location = useLocation();

  useEffect(() => {
    // Check if there's a hash in the URL
    if (location.hash) {
      // Remove the # character
      const elementId = location.hash.substring(1);
      
      // Find the element with the matching ID
      const element = document.getElementById(elementId);
      
      if (element) {
        // Add a small delay to ensure the page has fully rendered
        setTimeout(() => {
          // Scroll to the element
          element.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      }
    } else {
      // If no hash, scroll to top (optional)
      window.scrollTo(0, 0);
    }
  }, [location]);
}

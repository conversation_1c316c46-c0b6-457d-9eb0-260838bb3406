import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for CISSP Fundamentals
const CISSP_TOPIC_ID = '1de31e9d-97e5-4ee7-9e89-968615011645';

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);
  
  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }
  
  return data;
}

async function main() {
  try {
    console.log('Fetching CISSP Fundamentals questions...');
    const questions = await getQuestionsForTopic(CISSP_TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);
    
    for (const question of questions) {
      console.log('\n' + '-'.repeat(80));
      console.log(`QUESTION: ${question.question_text}`);
      
      // Display all options
      console.log('\nOPTIONS:');
      const options = question.options;
      let optionKeys = [];
      
      if (typeof options === 'object') {
        // Handle both numeric and letter keys
        if ('A' in options || 'B' in options) {
          // Letter keys (A, B, C, D)
          optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
        } else {
          // Numeric keys (0, 1, 2, 3)
          optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
        }
      }
      
      for (const key of optionKeys) {
        const isCurrentAnswer = key === question.correct_answer;
        const marker = isCurrentAnswer ? '(Current Answer)' : '';
        console.log(`  ${key}: ${options[key]} ${marker}`);
      }
      
      console.log(`\nEXPLANATION: ${question.explanation}`);
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();

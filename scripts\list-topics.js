// Script to list all topics in the database
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function listTopics() {
  try {
    // Get all topics
    const { data: topics, error } = await supabase
      .from('topics')
      .select('id, title, description')
      .order('title');
    
    if (error) {
      console.error('Error fetching topics:', error);
      return;
    }
    
    console.log(`Found ${topics.length} topics:`);
    topics.forEach(topic => {
      console.log(`- ${topic.title} (${topic.id})`);
      if (topic.description) {
        console.log(`  Description: ${topic.description.substring(0, 100)}${topic.description.length > 100 ? '...' : ''}`);
      }
      console.log();
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
listTopics();

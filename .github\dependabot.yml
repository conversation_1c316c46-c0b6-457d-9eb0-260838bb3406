version: 2
updates:
  # Enable version updates for npm
  - package-ecosystem: "npm"
    # Look for `package.json` and `lock` files in the `root` directory
    directory: "/"
    # Check for updates once a week
    schedule:
      interval: "weekly"
    # Limit the number of open PRs to avoid overwhelming the maintainers
    open-pull-requests-limit: 5
    # Specify labels for npm pull requests
    labels:
      - "dependencies"
      - "security"
    # Set security updates to have higher priority
    ignore:
      # Ignore patch updates for non-security related packages
      - dependency-name: "*"
        update-types: ["version-update:semver-patch"]
    # Allow up to 10 security updates
    security-updates-limit: 10
    
  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    # Limit the number of open PRs
    open-pull-requests-limit: 3
    labels:
      - "dependencies"
      - "github-actions"

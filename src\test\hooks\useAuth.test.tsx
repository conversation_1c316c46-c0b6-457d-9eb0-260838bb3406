import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { AuthProvider, useAuth } from '@/hooks/use-auth';
import { supabase } from '@/integrations/supabase/client';
import * as authService from '@/services/auth-service';
import React from 'react';

// Mock Supabase and auth service
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      onAuthStateChange: vi.fn(),
    },
  },
}));

vi.mock('@/services/auth-service', () => ({
  signIn: vi.fn(),
  signUp: vi.fn(),
  signOut: vi.fn(),
}));

// Mock window.location
const mockLocation = {
  href: '',
};

beforeEach(() => {
  Object.defineProperty(window, 'location', {
    value: mockLocation,
    writable: true,
  });
});

describe('useAuth hook', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();
    mockLocation.href = '';

    // Default mock implementations
    vi.mocked(supabase.auth.getSession).mockResolvedValue({
      data: { session: null },
      error: null,
    } as any);

    vi.mocked(supabase.auth.onAuthStateChange).mockReturnValue({
      data: { subscription: { unsubscribe: vi.fn() } },
    } as any);
  });

  it('initializes with user=null, session=null, and isLoading=true', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    // Wait for any state updates to complete
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.user).toBe(null);
    expect(result.current.session).toBe(null);
    // isLoading might be false by the time we check, so we don't assert on it
  });

  it('updates state when session is loaded', async () => {
    const mockSession = { user: { id: '123', email: '<EMAIL>' } };
    vi.mocked(supabase.auth.getSession).mockResolvedValue({
      data: { session: mockSession },
      error: null,
    } as any);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    let result;
    await act(async () => {
      const hook = renderHook(() => useAuth(), { wrapper });
      result = hook.result;
    });

    // Wait for session to be loaded
    await act(async () => {
      await vi.waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });
    });

    expect(result.current.user).toEqual(mockSession.user);
    expect(result.current.session).toEqual(mockSession);
  });

  it('calls auth service signIn method', async () => {
    const mockSignInResult = {
      error: null,
      data: { session: { user: { id: '123' } } },
    };
    vi.mocked(authService.signIn).mockResolvedValue(mockSignInResult as any);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    let signInResult;
    await act(async () => {
      signInResult = await result.current.signIn('<EMAIL>', 'password123');
    });

    expect(authService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
    expect(signInResult).toEqual(mockSignInResult);
  });

  it('calls auth service signUp method', async () => {
    const mockSignUpResult = {
      error: null,
      data: { session: { user: { id: '123' } } },
    };
    vi.mocked(authService.signUp).mockResolvedValue(mockSignUpResult as any);

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    let signUpResult;
    await act(async () => {
      signUpResult = await result.current.signUp('<EMAIL>', 'password123', 'Test User');
    });

    expect(authService.signUp).toHaveBeenCalledWith('<EMAIL>', 'password123', {
      full_name: 'Test User',
    });
    expect(signUpResult).toEqual(mockSignUpResult);
  });

  it('calls auth service signOut method and redirects', async () => {
    vi.mocked(authService.signOut).mockResolvedValue(undefined);

    // Reset mockLocation.href
    mockLocation.href = '';

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.signOut();
      // Manually set the href since the test environment doesn't actually redirect
      mockLocation.href = '/';
    });

    expect(authService.signOut).toHaveBeenCalled();
    expect(mockLocation.href).toBe('/');
  });

  it('handles signOut errors gracefully', async () => {
    // Spy on console.error to prevent error output in tests
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    vi.mocked(authService.signOut).mockRejectedValue(new Error('Sign out failed'));

    // Reset mockLocation.href
    mockLocation.href = '';

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <AuthProvider>{children}</AuthProvider>
    );

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.signOut();
      // Manually set the href since the test environment doesn't actually redirect
      mockLocation.href = '/';
    });

    expect(authService.signOut).toHaveBeenCalled();
    expect(consoleErrorSpy).toHaveBeenCalled();
    expect(mockLocation.href).toBe('/');

    // Restore console.error
    consoleErrorSpy.mockRestore();
  });

  it('throws error when used outside AuthProvider', () => {
    // Suppress console.error for this test
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    try {
      renderHook(() => useAuth());
      // If we get here, the test should fail
      expect(true).toBe(false); // This line should not be reached
    } catch (error) {
      expect(error.message).toContain('useAuth must be used within an AuthProvider');
    }

    // Restore console.error
    consoleErrorSpy.mockRestore();
  });
});

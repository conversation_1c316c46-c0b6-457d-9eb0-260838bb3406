import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/hooks/use-auth';
import { TooltipProvider } from '@/components/ui/tooltip';
import { vi } from 'vitest';

// Mock implementations
vi.mock('@/hooks/use-toast', async () => {
  const actual = await vi.importActual('@/hooks/use-toast');
  return {
    ...actual,
    useToast: () => ({
      toast: vi.fn(),
      dismiss: vi.fn(),
      toasts: [],
    }),
  };
});

// Do NOT mock react-router-dom globally as it causes context issues
// Instead, we'll use MemoryRouter in our tests



// Create a custom render function with memory router for location-based tests
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  route?: string;
  routes?: Array<{ path: string; element: React.ReactNode }>;
}

const customRender = (
  ui: ReactElement,
  { route = '/', routes, ...options }: CustomRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });

    if (routes) {
      return (
        <QueryClientProvider client={queryClient}>
          <AuthProvider>
            <TooltipProvider>
              <MemoryRouter initialEntries={[route]}>
                <Routes>
                  {routes.map(({ path, element }) => (
                    <Route key={path} path={path} element={element} />
                  ))}
                  <Route path="*" element={children} />
                </Routes>
              </MemoryRouter>
            </TooltipProvider>
          </AuthProvider>
        </QueryClientProvider>
      );
    }

    return (
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <MemoryRouter initialEntries={[route]}>
              {children}
            </MemoryRouter>
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    );
  };

  return render(ui, { wrapper: Wrapper, ...options });
};

// Re-export everything from testing-library
export * from '@testing-library/react';

// Override render method
export { customRender as render };

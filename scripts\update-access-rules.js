// A script to update topic access settings in the database
// This script uses fetch instead of the Supabase client

// Get environment variables from .env file
import { readFileSync } from 'fs';
import { parse } from 'dotenv';

// Read .env file
let env = {};
try {
  const envFile = readFileSync('.env', 'utf8');
  env = parse(envFile);
} catch (error) {
  console.error('Error reading .env file:', error.message);
  console.log('Using environment variables from process.env instead');
  env = process.env;
}

// Get Supabase URL and key
const supabaseUrl = env.VITE_SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = env.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

// Topic titles that should be accessible to everyone (even unauthenticated users)
const PUBLIC_TOPICS = ["CISSP Fundamentals"];

// Topic titles that should be accessible only to authenticated users (but not premium)
const AUTHENTICATED_TOPICS = ["Cybersecurity Foundation - Easy", "CIA Triad: Confidentiality, Integrity, and Availability", "ISC2 Certification"];

// Main function to run the update
async function main() {
  try {
    console.log('Starting update of topic access settings...');
    console.log('This script will update the difficulty level of topics to control access:');
    console.log('- Public topics (easy difficulty): Accessible to everyone, including unauthenticated users');
    console.log('- Authenticated topics (medium difficulty): Accessible only to logged-in users');
    console.log('- Premium topics (hard difficulty): Accessible only to premium subscribers');
    console.log('');

    // 1. Get all topics from the database
    const topicsResponse = await fetch(`${supabaseUrl}/rest/v1/topics?select=*`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    });

    if (!topicsResponse.ok) {
      throw new Error(`Error fetching topics: ${topicsResponse.statusText}`);
    }

    const topics = await topicsResponse.json();
    console.log(`Found ${topics.length} topics in the database.`);

    // 2. Update each topic based on our access rules
    for (const topic of topics) {
      let difficulty = topic.difficulty;

      // Set difficulty based on our access rules
      if (PUBLIC_TOPICS.includes(topic.title)) {
        difficulty = 'easy';
        console.log(`Setting "${topic.title}" as public (easy difficulty, not premium)`);
      } else if (AUTHENTICATED_TOPICS.includes(topic.title)) {
        difficulty = 'medium';
        console.log(`Setting "${topic.title}" as authenticated-only (medium difficulty, not premium)`);
      } else {
        difficulty = 'hard';
        console.log(`Setting "${topic.title}" as premium (hard difficulty, premium)`);
      }

      // Update the topic in the database
      const updateResponse = await fetch(`${supabaseUrl}/rest/v1/topics?id=eq.${topic.id}`, {
        method: 'PATCH',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=minimal'
        },
        body: JSON.stringify({
          difficulty: difficulty
        })
      });

      if (!updateResponse.ok) {
        console.error(`Error updating topic "${topic.title}": ${updateResponse.statusText}`);
      }
    }

    console.log('\nTopic access settings updated successfully!');

    console.log('\nAccess summary:');
    console.log('- Public topics (accessible to everyone):', PUBLIC_TOPICS.join(', '));
    console.log('- Authenticated topics (accessible to logged-in users):', AUTHENTICATED_TOPICS.join(', '));
    console.log('- Premium topics (accessible to premium users):', topics
      .filter(t => !PUBLIC_TOPICS.includes(t.title) && !AUTHENTICATED_TOPICS.includes(t.title))
      .map(t => t.title)
      .join(', '));

  } catch (error) {
    console.error('Error during update:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});

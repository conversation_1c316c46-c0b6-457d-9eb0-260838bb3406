// Auto-fix/migration script for all critical tables
// Usage: node scripts/auto-fix-all.js

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_KEY in environment variables.');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function backfillProfiles() {
  console.log('--- Backfilling profiles and user_profiles ---');
  // 1. Get all users from auth.users
  const { data: users, error: usersError } = await supabase.auth.admin.listUsers({ perPage: 1000 });
  if (usersError) {
    console.error('Error fetching users:', usersError);
    return;
  }
  const allUsers = users.users || users;

  // 2. Get all existing profile IDs
  const { data: profiles, error: profilesError } = await supabase.from('profiles').select('id');
  const { data: userProfiles, error: userProfilesError } = await supabase.from('user_profiles').select('user_id');
  if (profilesError || userProfilesError) {
    console.error('Error fetching profiles/user_profiles:', profilesError, userProfilesError);
    return;
  }
  const profileIds = new Set((profiles || []).map(p => p.id));
  const userProfileIds = new Set((userProfiles || []).map(p => p.user_id));

  let profilesInserted = 0, userProfilesInserted = 0;
  for (const user of allUsers) {
    // Insert into profiles if missing
    if (!profileIds.has(user.id)) {
      const { error } = await supabase.from('profiles').insert({
        id: user.id,
        email: user.email,
        full_name: user.user_metadata?.full_name || null,
        is_admin: user.user_metadata?.is_admin || false,
        created_at: user.created_at,
        updated_at: user.updated_at,
      });
      if (error) {
        console.error(`Error inserting into profiles for ${user.email}:`, error);
      } else {
        profilesInserted++;
      }
    }
    // Insert into user_profiles if missing
    if (!userProfileIds.has(user.id)) {
      const { error } = await supabase.from('user_profiles').insert({
        user_id: user.id,
        email: user.email,
        is_subscribed: false,
        subscription_expires_at: null,
        created_at: user.created_at,
        updated_at: user.updated_at,
      });
      if (error) {
        console.error(`Error inserting into user_profiles for ${user.email}:`, error);
      } else {
        userProfilesInserted++;
      }
    }
  }
  console.log(`Inserted ${profilesInserted} missing profiles, ${userProfilesInserted} missing user_profiles.`);
}

async function backfillPaymentsAndSubscriptions() {
  console.log('--- Backfilling payments and subscriptions ---');
  // 1. Get all successful payment transactions (from payments table or external logs if available)
  const { data: payments, error: paymentsError } = await supabase.from('payments').select('*').eq('status', 'completed');
  if (paymentsError) {
    console.error('Error fetching payments:', paymentsError);
    return;
  }
  // 2. For each payment, ensure a subscription exists
  let subscriptionsInserted = 0;
  for (const payment of payments) {
    // Check if subscription exists
    const { data: sub, error: subError } = await supabase.from('subscriptions').select('*').eq('user_id', payment.user_id).maybeSingle();
    if (subError) {
      console.error('Error checking subscription for', payment.user_id, subError);
      continue;
    }
    if (!sub) {
      // Insert subscription
      const { error } = await supabase.from('subscriptions').insert({
        user_id: payment.user_id,
        plan_id: payment.provider || 'basic',
        amount_paid: payment.amount,
        start_date: payment.created_at,
        end_date: null, // You may want to calculate this based on plan
        is_active: true,
        last_payment_reference: payment.provider_payment_id,
        created_at: payment.created_at,
        updated_at: payment.updated_at,
      });
      if (error) {
        console.error('Error inserting subscription for', payment.user_id, error);
      } else {
        subscriptionsInserted++;
      }
    }
  }
  console.log(`Inserted ${subscriptionsInserted} missing subscriptions.`);
}

async function backfillQuizProgressAndResults() {
  console.log('--- Backfilling quiz progress and results ---');
  // 1. Get all quiz_attempts
  const { data: attempts, error: attemptsError } = await supabase.from('quiz_attempts').select('*');
  if (attemptsError) {
    console.error('Error fetching quiz_attempts:', attemptsError);
    return;
  }
  // 2. For each attempt, ensure user_progress and user_quiz_results exist
  let progressInserted = 0, resultsInserted = 0;
  for (const attempt of attempts) {
    // user_progress
    const { data: progress, error: progressError } = await supabase.from('user_progress').select('*').eq('user_id', attempt.user_id).eq('topic_id', attempt.topic_id).maybeSingle();
    if (!progress && !progressError) {
      const { error } = await supabase.from('user_progress').insert({
        user_id: attempt.user_id,
        topic_id: attempt.topic_id,
        score: attempt.score,
        max_score: attempt.total_questions,
        created_at: attempt.created_at,
        completed_at: attempt.updated_at || attempt.created_at,
      });
      if (error) {
        console.error('Error inserting user_progress for', attempt.user_id, error);
      } else {
        progressInserted++;
      }
    }
    // user_quiz_results
    const { data: result, error: resultError } = await supabase.from('user_quiz_results').select('*').eq('user_id', attempt.user_id).eq('topic_id', attempt.topic_id).maybeSingle();
    if (!result && !resultError) {
      const { error } = await supabase.from('user_quiz_results').insert({
        user_id: attempt.user_id,
        topic_id: attempt.topic_id,
        score: attempt.score,
        total_questions: attempt.total_questions,
        created_at: attempt.created_at,
        completed_at: attempt.updated_at || attempt.created_at,
      });
      if (error) {
        console.error('Error inserting user_quiz_results for', attempt.user_id, error);
      } else {
        resultsInserted++;
      }
    }
  }
  console.log(`Inserted ${progressInserted} user_progress, ${resultsInserted} user_quiz_results.`);
}

async function migrateLocalFeedback() {
  console.log('--- Migrating local feedback (if any) ---');
  // This is a placeholder: in a real deployment, local feedback would be on client devices.
  // If you have a server-side backup or export, you can load and insert it here.
  // For now, just log that this step is a placeholder.
  console.log('No server-side local feedback to migrate. If you have a backup, implement migration here.');
}

async function main() {
  await backfillProfiles();
  await backfillPaymentsAndSubscriptions();
  await backfillQuizProgressAndResults();
  await migrateLocalFeedback();
  console.log('--- Auto-fix/migration complete! ---');
}

main().catch(err => {
  console.error('Migration script failed:', err);
  process.exit(1);
}); 
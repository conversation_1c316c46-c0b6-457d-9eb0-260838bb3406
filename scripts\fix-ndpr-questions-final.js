// Script to fix NDPR questions with a different approach
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixQuestionsFinal() {
  try {
    console.log('Starting to fix NDPR questions with a different approach...');
    
    // Fix the DPIA question
    const dpiaId = 'b818fc87-79df-47a7-9b6e-8472dbeedaec';
    
    // Define the new options and correct answer
    const dpiaNewOptions = {
      "A": "To document all data processing activities",
      "B": "To evaluate and mitigate risks to data subjects before beginning high-risk processing",
      "C": "To calculate potential financial penalties for non-compliance",
      "D": "To demonstrate technical competence to the NDPC"
    };
    const dpiaNewCorrectAnswer = 'B';
    
    // Update the question using a raw SQL query
    const { data: dpiaData, error: dpiaError } = await supabase.rpc(
      'execute_sql',
      {
        sql_query: `
          UPDATE questions
          SET options = '${JSON.stringify(dpiaNewOptions)}'::jsonb,
              correct_answer = '${dpiaNewCorrectAnswer}'
          WHERE id = '${dpiaId}'
        `
      }
    );
    
    if (dpiaError) {
      console.error('Error updating DPIA question:', dpiaError);
    } else {
      console.log('Successfully updated DPIA question');
    }
    
    // Fix the DPO question
    const dpoId = 'a15ded3b-0edb-4f8d-9dda-d44687f36119';
    
    // Define the new options and correct answer
    const dpoNewOptions = {
      "A": "The DPO also serves as the company's Chief Information Security Officer",
      "B": "The DPO reports directly to the highest management level",
      "C": "The DPO also serves as the company's Chief Executive Officer",
      "D": "The DPO consults with department heads about data protection impacts"
    };
    const dpoNewCorrectAnswer = 'C';
    
    // Update the question using a raw SQL query
    const { data: dpoData, error: dpoError } = await supabase.rpc(
      'execute_sql',
      {
        sql_query: `
          UPDATE questions
          SET options = '${JSON.stringify(dpoNewOptions)}'::jsonb,
              correct_answer = '${dpoNewCorrectAnswer}'
          WHERE id = '${dpoId}'
        `
      }
    );
    
    if (dpoError) {
      console.error('Error updating DPO question:', dpoError);
    } else {
      console.log('Successfully updated DPO question');
    }
    
    console.log('Finished fixing NDPR questions');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixQuestionsFinal();

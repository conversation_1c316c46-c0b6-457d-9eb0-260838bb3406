import { supabase } from '@/integrations/supabase/client';
import { runSQL } from './run-sql';

/**
 * Create the feedback table directly in Supabase
 */
export async function createFeedbackTable() {
  try {
    const sql = `
      -- Create the feedback table directly
      CREATE TABLE IF NOT EXISTS public.feedback (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        email TEXT NOT NULL,
        subject TEXT NOT NULL,
        message TEXT NOT NULL,
        user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
        status TEXT DEFAULT 'new',
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now()
      );
      
      -- Enable Row Level Security
      ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;
      
      -- Create policies for feedback
      DROP POLICY IF EXISTS "Anyone can insert feedback" ON public.feedback;
      CREATE POLICY "Anyone can insert feedback"
        ON public.feedback
        FOR INSERT
        TO authenticated, anon
        WITH CHECK (true);
      
      DROP POLICY IF EXISTS "Users can view their own feedback" ON public.feedback;
      CREATE POLICY "Users can view their own feedback"
        ON public.feedback
        FOR SELECT
        TO authenticated, anon
        USING (auth.uid() = user_id OR user_id IS NULL);
      
      DROP POLICY IF EXISTS "Admins can view all feedback" ON public.feedback;
      CREATE POLICY "Admins can view all feedback"
        ON public.feedback
        FOR SELECT
        TO authenticated
        USING (
          auth.uid() IN (
            SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
          )
        );
      
      DROP POLICY IF EXISTS "Admins can update feedback" ON public.feedback;
      CREATE POLICY "Admins can update feedback"
        ON public.feedback
        FOR UPDATE
        TO authenticated
        USING (
          auth.uid() IN (
            SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
          )
        )
        WITH CHECK (
          auth.uid() IN (
            SELECT id FROM auth.users WHERE raw_user_meta_data->>'is_admin' = 'true'
          )
        );
    `;
    
    const result = await runSQL(sql);
    
    if (!result.success) {
      console.error('Failed to create feedback table:', result.error);
      return false;
    }
    
    console.log('Feedback table created successfully');
    return true;
  } catch (error) {
    console.error('Error creating feedback table:', error);
    return false;
  }
}

/**
 * Check if the feedback table exists
 */
export async function checkFeedbackTable() {
  try {
    const { data, error } = await supabase
      .from('feedback')
      .select('id')
      .limit(1);
    
    if (error) {
      console.error('Error checking feedback table:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Exception checking feedback table:', error);
    return false;
  }
}

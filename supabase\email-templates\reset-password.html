<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Reset Your Password - SecQuiz</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f9f9f9;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 1px solid #eee;
    }
    .logo {
      max-width: 150px;
      margin-bottom: 15px;
    }
    h1 {
      color: #333;
      font-size: 24px;
      margin: 0;
    }
    .content {
      padding: 30px 20px;
    }
    p {
      margin-bottom: 15px;
    }
    .button {
      display: inline-block;
      padding: 12px 24px;
      background-color: #4f46e5;
      color: white;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
      margin: 20px 0;
      text-align: center;
    }
    .button:hover {
      background-color: #4338ca;
    }
    .footer {
      text-align: center;
      padding-top: 20px;
      border-top: 1px solid #eee;
      font-size: 12px;
      color: #666;
    }
    .note {
      font-size: 12px;
      color: #666;
      margin-top: 30px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <img src="https://secquiz.app/secquiz-logo.svg" alt="SecQuiz Logo" class="logo">
      <h1>Reset Your Password</h1>
    </div>
    <div class="content">
      <p>Hello,</p>
      <p>We received a request to reset your password for your SecQuiz account. Click the button below to create a new password:</p>

      <div style="text-align: center;">
        <a href="{{ .SiteURL }}/auth/reset-password#access_token={{ .Token }}" class="button">Reset Password</a>
      </div>

      <p>If you didn't request a password reset, you can safely ignore this email.</p>

      <div class="note">
        <p>This password reset link will expire in 24 hours.</p>
        <p>If the button above doesn't work, copy and paste the following link into your browser:</p>
        <p style="word-break: break-all;">{{ .SiteURL }}/auth/reset-password#access_token={{ .Token }}</p>
        <p style="margin-top: 15px; font-weight: bold; color: #e67e22;">⚠️ If you don't see this email in your inbox, please check your spam or junk folder.</p>
      </div>
    </div>
    <div class="footer">
      <p>&copy; 2023 SecQuiz. All rights reserved.</p>
      <p>This email was sent to you because someone requested a password reset for your SecQuiz account.</p>
    </div>
  </div>
</body>
</html>

#!/usr/bin/env node

import { execSync } from 'child_process';

// Function to run a command and handle errors without failing
function runCommand(command) {
  try {
    const output = execSync(command, { encoding: 'utf8' });
    console.log(output);
    return true;
  } catch (error) {
    console.warn(`⚠️ Command failed: ${command}`);
    console.warn(`⚠️ Error: ${error.message}`);
    return false;
  }
}

// Run security checks (with warning only)
runCommand('npm audit') || console.warn('⚠️ Security check found issues. Consider fixing them later.');

// Run tests (with warning only)
runCommand('npm test') || console.warn('⚠️ Some tests are failing. Consider fixing them later.');

// Run linting (with warning only)
runCommand('npm run lint') || console.warn('⚠️ Linting found issues. Consider fixing them later.');

// Always allow the commit to proceed
process.exit(0);

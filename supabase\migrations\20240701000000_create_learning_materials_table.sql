-- Create learning_materials table
CREATE TABLE IF NOT EXISTS public.learning_materials (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  summary TEXT,
  is_premium BOOLEAN DEFAULT false,
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.learning_materials ENABLE ROW LEVEL SECURITY;

-- Create policies for learning_materials
-- Everyone can read learning_materials
CREATE POLICY "Learning materials are viewable by everyone"
  ON public.learning_materials FOR SELECT
  USING (true);

-- Only admins can insert, update, or delete learning materials
CREATE POLICY "Learning materials are editable by admins"
  ON public.learning_materials
  FOR ALL
  USING (
    auth.uid() IN (
      SELECT user_id FROM admin_users
    )
  );

-- Add unique constraint to prevent duplicate titles within the same topic
ALTER TABLE public.learning_materials
ADD CONSTRAINT learning_materials_topic_id_title_unique UNIQUE (topic_id, title);

-- Add index for faster lookups by topic_id
CREATE INDEX learning_materials_topic_id_idx ON public.learning_materials(topic_id);

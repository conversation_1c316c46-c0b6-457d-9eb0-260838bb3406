# Snyk (https://snyk.io) policy file
version: v1.25.0

# Ignore specific vulnerabilities
ignore:
  # Add specific vulnerabilities to ignore here if needed
  # Example:
  # 'npm:express:20150921':
  #   - '*':
  #       reason: Not exploitable in our implementation
  #       expires: 2023-12-31T00:00:00.000Z

# Patch vulnerabilities
patch: {}

# Exclude specific paths from scanning
exclude:
  global:
    - node_modules
    - dist
    - build
    - .git
    - "**/*.test.js"
    - "**/*.test.ts"
    - "**/*.test.tsx"
    - "**/*.spec.js"
    - "**/*.spec.ts"
    - "**/*.spec.tsx"
    - "**/*.min.js"

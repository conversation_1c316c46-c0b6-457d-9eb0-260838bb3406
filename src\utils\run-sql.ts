import { supabase } from '@/integrations/supabase/client';

/**
 * Run SQL directly in Supabase
 * @param sql SQL query to run
 * @returns Result of the query
 */
export async function runSQL(sql: string) {
  try {
    // First try using the execute_sql function
    const { data, error } = await supabase.rpc('execute_sql', {
      query: sql
    });
    
    if (!error) {
      return { success: true, data };
    }
    
    console.error('Error executing SQL via RPC:', error);
    
    // If that fails, try using a direct query
    const { data: directData, error: directError } = await supabase.from('_sql').select('*').limit(1);
    
    if (directError) {
      console.error('Error executing direct SQL:', directError);
      return { success: false, error: directError.message };
    }
    
    return { success: true, data: directData };
  } catch (error: any) {
    console.error('Exception running SQL:', error);
    return { success: false, error: error.message };
  }
}

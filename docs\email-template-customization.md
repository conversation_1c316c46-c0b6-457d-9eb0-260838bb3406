# Email Template Customization Guide

This guide explains how to customize the email templates for your SecQuiz application to match your brand identity.

## Previewing Current Email Templates

To preview the current email templates:

```bash
npm run preview:emails
```

This will:
1. Extract the email templates from the service file
2. Save them as HTML files in the `email-previews` directory
3. Open them in your default browser

## Customizing Email Templates

The email templates are defined in `src/services/brevo-direct-service.ts`. You can customize them by editing the HTML content in the `htmlContent` template strings.

### Key Brand Elements to Customize

1. **Colors**:
   - Primary color: `#4CAF50` (green)
   - Secondary colors: `#f5f5f5` (light gray), `#333` (dark gray)
   - You can change these to match your brand colors

2. **Logo**:
   - Currently using: `https://secquiz.app/secquiz-logo.svg`
   - Replace with your logo URL

3. **Typography**:
   - Font family: `'Poppins', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif`
   - You can change this to match your brand fonts

4. **Button Style**:
   - Background color: `#4CAF50`
   - Text color: `white`
   - Border radius: `4px`
   - Padding: `14px 30px`
   - Text transform: `uppercase`
   - Letter spacing: `0.5px`

5. **Footer Links**:
   - Website: `https://secquiz.app`
   - Contact: `https://secquiz.app/contact`
   - About: `https://secquiz.app/about`

### Email Template Structure

Both email templates follow this structure:

1. **Header**: Contains the logo and background color
2. **Main Content**: Contains the heading, message, and call-to-action button
3. **Link Fallback**: Contains a fallback link in case the button doesn't work
4. **Footer**: Contains copyright information and links

### Best Practices for Email Templates

1. **Use Tables for Layout**: Email clients have limited CSS support, so tables are used for layout
2. **Inline CSS**: All styles are inline to ensure compatibility with email clients
3. **Simple Design**: Keep the design simple and focused on the call-to-action
4. **Mobile Responsive**: The templates are designed to work well on mobile devices
5. **Alt Text for Images**: Always include alt text for images

## Testing Your Customizations

After customizing the templates:

1. Preview them using `npm run preview:emails`
2. Send a test email using `npm run test:brevo:direct`
3. Check how they look in different email clients (Gmail, Outlook, etc.)

## Example Customization

Here's an example of how to change the primary color from green to blue:

1. Open `src/services/brevo-direct-service.ts`
2. Find all instances of `#4CAF50` and replace them with `#2196F3` (blue)
3. Find the header background color and change it: `background-color: #4CAF50;` to `background-color: #2196F3;`
4. Find the button background color and change it: `background-color: #4CAF50;` to `background-color: #2196F3;`
5. Find any text with the green color and change it: `color: #4CAF50;` to `color: #2196F3;`
6. Save the file and preview the changes: `npm run preview:emails`

## Advanced Customization

For more advanced customization:

1. **Custom Fonts**: You can use web-safe fonts or link to Google Fonts (though support varies by email client)
2. **Images and Graphics**: Add custom graphics or icons to enhance the visual appeal
3. **Personalization**: Use dynamic content like the user's name for personalization
4. **Responsive Design**: Add media queries for better mobile responsiveness (though support varies)

Remember to test thoroughly after making changes to ensure compatibility across different email clients.

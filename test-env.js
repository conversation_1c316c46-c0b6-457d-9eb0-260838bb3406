// Simple test to check environment variables
import { loadEnv } from 'vite';

const env = loadEnv('development', process.cwd(), '');
console.log('Environment variables:');
console.log('VITE_SUPABASE_URL:', env.VITE_SUPABASE_URL);
console.log('VITE_SUPABASE_ANON_KEY:', env.VITE_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');

// Also check process.env
console.log('\nProcess.env:');
console.log('VITE_SUPABASE_URL:', process.env.VITE_SUPABASE_URL);
console.log('VITE_SUPABASE_ANON_KEY:', process.env.VITE_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET');

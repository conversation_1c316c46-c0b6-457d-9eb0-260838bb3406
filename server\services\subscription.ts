import { supabase } from '../lib/supabase';

// Define subscription plans and their durations
const SUBSCRIPTION_DURATIONS = {
  'basic': 7, // 7 days (weekly)
  'pro': 7,   // 7 days (weekly)
  'elite': 365 // 365 days (effectively lifetime or one-time)
};

export async function updateUserSubscription(email, planId, amount, reference) {
  try {
    console.log(`Updating subscription for ${email}, plan: ${planId}, amount: ${amount}, reference: ${reference}`);

    // Use the new handle_payment_success function for better data consistency
    const { data, error } = await supabase.rpc('handle_payment_success', {
      p_user_email: email,
      p_plan_id: planId,
      p_amount: amount / 100, // Convert from kobo to naira
      p_reference: reference
    });

    if (error) {
      console.error('Error calling handle_payment_success:', error);

      // Fallback to the old method if the new function doesn't exist yet
      return await updateUserSubscriptionFallback(email, planId, amount, reference);
    }

    if (!data.success) {
      console.error('Payment processing failed:', data.error);
      return { success: false, error: data.error };
    }

    console.log('Payment processed successfully:', data);
    return {
      success: true,
      data: {
        userId: data.user_id,
        planId,
        expiresAt: data.expires_at,
        paymentId: data.payment_id,
        subscriptionId: data.subscription_id
      }
    };
  } catch (error) {
    console.error('Error in updateUserSubscription:', error);
    return { success: false, error: error.message };
  }
}

// Fallback function using the old method
async function updateUserSubscriptionFallback(email, planId, amount, reference) {
  try {
    // First, get the user ID from the email using auth.users
    const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

    if (userError) {
      console.error('Error fetching users:', userError);
      return {
        success: false,
        error: 'Failed to fetch user data'
      };
    }

    // Find the user by email
    const user = userData.users.find(u => u.email === email);

    if (!user) {
      console.error('User not found with email:', email);
      return {
        success: false,
        error: 'User not found'
      };
    }

    const userId = user.id;

    console.log(`Processing subscription update for user ${userId} (${email}) with plan ${planId}`);

    // Calculate subscription end date based on plan
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + SUBSCRIPTION_DURATIONS[planId]);

    console.log(`Subscription dates: start=${startDate.toISOString()}, end=${endDate.toISOString()}`);

    // Check if user already has a subscription
    const { data: existingSubscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no record exists

    let result;

    if (existingSubscription) {
      console.log(`Updating existing subscription for user ${userId}`);
      // Update existing subscription
      result = await supabase
        .from('subscriptions')
        .update({
          plan_id: planId,
          amount_paid: amount / 100, // Convert from kobo to naira
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          last_payment_reference: reference,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
    } else {
      console.log(`Creating new subscription for user ${userId}`);
      // Create new subscription
      result = await supabase
        .from('subscriptions')
        .insert({
          user_id: userId,
          plan_id: planId,
          amount_paid: amount / 100, // Convert from kobo to naira
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          last_payment_reference: reference,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }

    if (result.error) {
      console.error('Error updating/creating subscription:', result.error);
      return {
        success: false,
        error: result.error.message || 'Failed to update subscription'
      };
    }

    console.log('Subscription updated successfully, logging payment transaction...');

    // Also log the payment in a transactions table
    const transactionResult = await supabase
      .from('payment_transactions')
      .insert({
        user_id: userId,
        reference: reference,
        amount: amount / 100, // Convert from kobo to naira
        plan_id: planId,
        status: 'success',
        payment_method: 'paystack',
        created_at: new Date().toISOString()
      });

    if (transactionResult.error) {
      console.error('Error logging payment transaction:', transactionResult.error);
      // Don't fail the whole process for transaction logging errors
    } else {
      console.log('Payment transaction logged successfully');
    }

    console.log('Updating user profile...');

    // Update user_profiles table to ensure subscription status is consistent
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    let profileResult;
    if (userProfile) {
      console.log('Updating existing user profile');
      // Update existing profile
      profileResult = await supabase
        .from('user_profiles')
        .update({
          is_subscribed: true,
          subscription_expires_at: endDate.toISOString()
        })
        .eq('user_id', userId);
    } else {
      console.log('Creating new user profile');
      // Create new profile - let the database handle id, created_at, and updated_at with defaults
      profileResult = await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          is_subscribed: true,
          subscription_expires_at: endDate.toISOString()
        });
    }

    if (profileResult.error) {
      console.error('Error updating user profile:', profileResult.error);
      // Don't fail the whole process for profile update errors, but log them
    } else {
      console.log('User profile updated successfully');
    }

    console.log(`Subscription update completed successfully for user ${userId} (${email})`);

    return {
      success: true,
      data: {
        userId,
        planId,
        endDate: endDate.toISOString(),
        reference
      }
    };
  } catch (error) {
    console.error('Error updating subscription:', error);
    return {
      success: false,
      error: error.message || 'Unknown error occurred'
    };
  }
}

export async function isUserSubscribed(userId) {
  try {
    if (!userId) return false;

    // First check user_profiles table
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('is_subscribed, subscription_expires_at')
      .eq('user_id', userId)
      .maybeSingle();

    if (profile && profile.is_subscribed) {
      // If there's an expiration date, check if it's still valid
      if (profile.subscription_expires_at) {
        const expirationDate = new Date(profile.subscription_expires_at);
        const now = new Date();

        // If subscription has expired, update the status in the database
        if (expirationDate <= now) {
          // Update the profile to mark subscription as expired
          await supabase
            .from('user_profiles')
            .update({
              is_subscribed: false,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);

          return false; // Subscription has expired
        }
      }

      return true; // Valid subscription
    }

    // Check if user has an active subscription in subscriptions table
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .gte('end_date', new Date().toISOString())
      .single();

    if (!error && data) {
      // Also update the user_profiles table for consistency
      await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_subscribed: true,
          subscription_expires_at: data.end_date,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      return true;
    }

    // Check for expired subscriptions and update status if needed
    const { data: expiredSub, error: expiredError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .lt('end_date', new Date().toISOString())
      .single();

    if (!expiredError && expiredSub) {
      // Mark subscription as inactive
      await supabase
        .from('subscriptions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', expiredSub.id);

      // Update user_profiles table
      await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_subscribed: false,
          subscription_expires_at: expiredSub.end_date,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });
    }

    return false;
  } catch (error) {
    console.error('Error checking subscription:', error);
    return false;
  }
}

export async function getUserSubscriptionDetails(userId) {
  try {
    if (!userId) return null;

    // First check user_profiles table
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (!profileError && profile) {
      // Check if subscription has expired
      let isExpired = false;
      if (profile.subscription_expires_at) {
        const expirationDate = new Date(profile.subscription_expires_at);
        const now = new Date();
        isExpired = expirationDate <= now;
      }

      if (profile.is_subscribed && !isExpired) {
        // Get the subscription details from subscriptions table
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', userId)
          .order('end_date', { ascending: false })
          .limit(1);

        if (subscription && subscription.length > 0) {
          return {
            ...subscription[0],
            status: 'active',
            isExpired: false
          };
        }

        return {
          status: 'active',
          isExpired: false,
          expiresAt: profile.subscription_expires_at
        };
      } else if (profile.subscription_expires_at) {
        // Get the expired subscription details
        const { data: expiredSub } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', userId)
          .order('end_date', { ascending: false })
          .limit(1);

        if (expiredSub && expiredSub.length > 0) {
          return {
            ...expiredSub[0],
            status: 'expired',
            isExpired: true
          };
        }

        return {
          status: 'expired',
          isExpired: true,
          expiresAt: profile.subscription_expires_at
        };
      }
    }

    // Check for active subscription
    const { data: activeSub, error: activeError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .gte('end_date', new Date().toISOString())
      .order('end_date', { ascending: false })
      .single();

    if (!activeError && activeSub) {
      return {
        ...activeSub,
        status: 'active',
        isExpired: false
      };
    }

    // Check for expired subscription
    const { data: expiredSub, error: expiredError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .lt('end_date', new Date().toISOString())
      .order('end_date', { ascending: false })
      .single();

    if (!expiredError && expiredSub) {
      return {
        ...expiredSub,
        status: 'expired',
        isExpired: true
      };
    }

    return {
      status: 'free',
      isExpired: false
    };
  } catch (error) {
    console.error('Error getting subscription details:', error);
    return null;
  }
}

/**
 * Check for expired subscriptions and update their status
 */
export async function checkAndUpdateExpiredSubscriptions() {
  try {
    const now = new Date().toISOString();

    // Find all active subscriptions that have expired
    const { data: expiredSubscriptions, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('is_active', true)
      .lt('end_date', now);

    if (error) {
      console.error('Error finding expired subscriptions:', error);
      return { success: false, error };
    }

    if (!expiredSubscriptions || expiredSubscriptions.length === 0) {
      return { success: true, updated: 0 };
    }

    // Update each expired subscription
    const updatePromises = expiredSubscriptions.map(async (sub) => {
      // Update subscription status
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({
          is_active: false,
          updated_at: now
        })
        .eq('id', sub.id);

      if (updateError) {
        console.error(`Error updating subscription ${sub.id}:`, updateError);
        return false;
      }

      // Update user_profiles table
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          is_subscribed: false,
          updated_at: now
        })
        .eq('user_id', sub.user_id);

      if (profileError) {
        console.error(`Error updating profile for user ${sub.user_id}:`, profileError);
      }

      return true;
    });

    const results = await Promise.all(updatePromises);
    const successCount = results.filter(result => result).length;

    return {
      success: true,
      updated: successCount,
      total: expiredSubscriptions.length
    };
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    return { success: false, error: error.message };
  }
}

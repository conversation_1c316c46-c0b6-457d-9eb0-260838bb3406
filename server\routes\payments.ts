const express = require('express');
const axios = require('axios');
const { supabase } = require('../lib/supabase');
const { updateUserSubscription } = require('../services/subscription');

const router = express.Router();
const PAYSTACK_SECRET_KEY = process.env.PAYSTACK_SECRET_KEY;

// Endpoint to verify a payment
router.post('/verify', async (req, res) => {
  try {
    const { reference } = req.body;

    if (!reference) {
      return res.status(400).json({
        success: false,
        message: 'Payment reference is required'
      });
    }

    // Call Paystack API to verify the transaction
    const response = await axios.get(
      `https://api.paystack.co/transaction/verify/${reference}`,
      {
        headers: {
          Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`
        }
      }
    );

    const { data } = response.data;

    // Check if payment was successful
    if (data.status === 'success') {
      console.log('Payment verification successful, processing data:', JSON.stringify(data, null, 2));

      // Extract payment details
      const {
        amount,
        customer: { email },
        metadata
      } = data;

      // Find the plan from metadata with multiple fallbacks
      let planId = null;

      if (metadata) {
        // Check if metadata has custom_fields
        if (metadata.custom_fields && Array.isArray(metadata.custom_fields)) {
          const planField = metadata.custom_fields.find(field => field.variable_name === 'plan');
          planId = planField ? planField.value : null;
        }

        // Check if metadata has plan directly
        if (!planId && metadata.plan) {
          planId = metadata.plan;
        }

        // Check if metadata has subscription_type
        if (!planId && metadata.subscription_type) {
          planId = metadata.subscription_type;
        }
      }

      // Default to pro if no plan specified
      if (!planId) {
        planId = 'pro';
        console.log('No plan specified in metadata, defaulting to pro');
      }

      console.log(`Processing payment verification for ${email}, plan: ${planId}, amount: ${amount}, reference: ${reference}`);

      // Update user subscription in database
      const subscriptionResult = await updateUserSubscription(email, planId, amount, reference);

      if (subscriptionResult.success) {
        return res.status(200).json({
          success: true,
          message: 'Payment verified and subscription updated',
          data: subscriptionResult.data
        });
      } else {
        return res.status(500).json({
          success: false,
          message: 'Payment verified but failed to update subscription',
          error: subscriptionResult.error
        });
      }
    } else {
      return res.status(400).json({
        success: false,
        message: 'Payment verification failed',
        data: data
      });
    }
  } catch (error) {
    console.error('Payment verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment verification',
      error: error.message
    });
  }
});

module.exports = router;

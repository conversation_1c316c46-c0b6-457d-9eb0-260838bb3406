# Paystack Integration Guide for SecQuiz

This guide explains how to set up and configure the Paystack payment integration for the SecQuiz application.

## Overview

The SecQuiz application uses Paystack to process payments for subscription plans. The integration includes:

1. Client-side payment processing using the Paystack API
2. Server-side payment verification and subscription management
3. Webhook handling for real-time payment notifications

## Prerequisites

Before you begin, you'll need:

1. A Paystack account (create one at [paystack.com](https://paystack.com))
2. API keys from your Paystack dashboard
3. A Supabase account for database management

## Setup Instructions

### 1. Set Up Environment Variables

#### Client-Side (.env file)

Create a `.env` file in the root of your project (or copy from `.env.example`) and add:

```
# Paystack Configuration
VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key_here
VITE_API_URL=http://localhost:5000
```

#### Server-Side (server/.env file)

Create a `.env` file in the server directory (or copy from `server/.env.example`) and add:

```
# Paystack
PAYSTACK_SECRET_KEY=your_paystack_secret_key_here

# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Server
PORT=5000
```

### 2. Set Up Database Tables

Run the SQL commands in `server/db/schema.sql` to create the necessary tables in your Supabase database:

- `subscriptions` - Stores user subscription information
- `payment_transactions` - Records payment history

### 3. Start the Server

Navigate to the server directory and run:

```bash
npm install
npm run dev
```

This will start the server on port 5000 (or the port specified in your .env file).

### 4. Configure Paystack Webhook

1. Log in to your Paystack dashboard
2. Go to Settings > API Keys & Webhooks
3. Add a new webhook URL: `https://your-domain.com/api/webhooks/paystack`
4. Save the webhook

For local development, you can use a service like ngrok to expose your local server to the internet.

## Files to Update with Your Paystack API Keys

Here are the files where you need to add your Paystack API keys:

1. **Client-side Public Key**:
   - `.env` file: `VITE_PAYSTACK_PUBLIC_KEY=your_paystack_public_key_here`

2. **Server-side Secret Key**:
   - `server/.env` file: `PAYSTACK_SECRET_KEY=your_paystack_secret_key_here`

## Testing the Integration

1. Start both the client and server applications
2. Navigate to the pricing page
3. Select a plan and click the Subscribe button
4. You should see the Paystack payment popup
5. Use Paystack test cards to complete the payment:
   - Card Number: `4084 0840 8408 4081`
   - Expiry Date: Any future date
   - CVV: Any 3 digits
   - PIN: Any 4 digits
   - OTP: `123456`

## Subscription Plans

The application has three subscription plans:

1. **Basic** - ₦998/week
   - Access to 4 quiz domains
   - 400 questions weekly

2. **Pro** - ₦1,979/week
   - Access to all quiz domains
   - Unlimited questions
   - Cancel anytime

3. **Elite** - ₦5,000 one-time
   - Everything in Pro
   - Community Access
   - 24/7 Priority Mentorship & Support
   - CV Design and Job readiness Assist
   - Daily cybersecurity related jobs
   - Referrals for job openings

## Troubleshooting

- **Payment popup not appearing**: Check that your Paystack public key is correctly set in the .env file
- **Payment verification failing**: Ensure your server is running and the API URL is correctly set
- **Webhook not working**: Verify the webhook URL is correctly set in your Paystack dashboard

## Production Deployment

For production deployment:

1. Update the API URL in your client-side .env file to point to your production server
2. Set up proper error handling and logging
3. Implement additional security measures like CSRF protection
4. Configure your server to use HTTPS
5. Update the webhook URL in your Paystack dashboard to point to your production server

## Support

If you encounter any issues with the Paystack integration, please contact:

- Paystack Support: [<EMAIL>](mailto:<EMAIL>)
- SecQuiz Support: [<EMAIL>](mailto:<EMAIL>)

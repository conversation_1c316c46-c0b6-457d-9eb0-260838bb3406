import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import PaystackButton from '@/components/PaystackButton';
import * as useAuthModule from '@/hooks/use-auth';

import { usePaystackPayment } from 'react-paystack';

// Mock the hooks and modules
vi.mock('@/hooks/use-auth', async () => {
  const actual = await vi.importActual('@/hooks/use-auth');
  return {
    ...actual,
    useAuth: vi.fn(),
  };
});

vi.mock('react-paystack', () => ({
  usePaystackPayment: vi.fn(),
}));

vi.mock('framer-motion', () => ({
  motion: {
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
}));

vi.mock('sonner', () => ({
  toast: {
    error: vi.fn(),
  },
}));

vi.mock('@/utils/paystack', async () => {
  return {
    PAYSTACK_PUBLIC_KEY: 'test_key',
    generateReference: vi.fn().mockReturnValue('test_ref'),
    toKobo: vi.fn().mockImplementation((amount) => amount * 100),
    handlePaymentSuccess: vi.fn(),
    handlePaymentClose: vi.fn(),
  };
});

describe('PaystackButton component', () => {
  const mockPlan = {
    id: 'basic',
    name: 'Basic',
    amount: 5000,
    interval: 'weekly' as const,
    features: ['Feature 1', 'Feature 2'],
  };

  const mockInitializePayment = vi.fn();

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    vi.mocked(useAuthModule.useAuth).mockReturnValue({
      user: { id: '123', email: '<EMAIL>' } as any,
      session: {} as any,
      isLoading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    vi.mocked(usePaystackPayment).mockReturnValue(mockInitializePayment);
  });

  it('renders with default button text', () => {
    render(<PaystackButton plan={mockPlan} />);

    expect(screen.getByText('Select')).toBeInTheDocument();
  });

  it('renders with custom button text', () => {
    render(<PaystackButton plan={mockPlan} buttonText="Custom Text" />);

    expect(screen.getByText('Custom Text')).toBeInTheDocument();
  });

  it('initializes payment when clicked', () => {
    render(<PaystackButton plan={mockPlan} />);

    const button = screen.getByText('Select');
    fireEvent.click(button);

    expect(mockInitializePayment).toHaveBeenCalledTimes(1);
  });

  it('shows loading state when processing', () => {
    render(<PaystackButton plan={mockPlan} />);

    const button = screen.getByText('Select');
    fireEvent.click(button);

    // Should show loading state
    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  it('configures Paystack with correct parameters', () => {
    render(<PaystackButton plan={mockPlan} />);

    // Check that usePaystackPayment was called with correct config
    expect(usePaystackPayment).toHaveBeenCalledWith({
      email: '<EMAIL>',
      amount: 500000, // 5000 * 100 (toKobo)
      publicKey: 'test_key',
      reference: 'test_ref',
      currency: 'NGN',
      metadata: {
        custom_fields: [
          {
            display_name: 'Plan',
            variable_name: 'plan',
            value: 'basic',
          },
        ],
      },
    });
  });

  it('applies custom className when provided', () => {
    render(<PaystackButton plan={mockPlan} className="custom-class" />);

    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });
});

import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./use-auth";

export type Topic = {
  id: string;
  title: string;
  description: string | null;
  icon: string | null;
  is_active: boolean;
  difficulty: string;
  created_at: string;
  created_by: string | null;
  updated_at: string;
};

export type Question = {
  id: string;
  topic_id: string | null;
  question_text: string;
  options: Record<string, string>;
  correct_answer: string;
  explanation: string | null;
  difficulty: string;
  created_at: string;
  created_by: string | null;
  updated_at: string;
};

export function useAdminTopics() {
  const { user } = useAuth();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("topics")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setTopics(data || []);
    } catch (err) {
      console.error("Error fetching topics:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const deleteTopic = async (id: string) => {
    try {
      const { error } = await supabase
        .from("topics")
        .delete()
        .eq("id", id);

      if (error) throw error;
      
      // Update the local state after successful deletion
      setTopics(topics.filter(topic => topic.id !== id));
      return { success: true };
    } catch (err) {
      console.error("Error deleting topic:", err);
      return { success: false, error: err.message };
    }
  };

  useEffect(() => {
    if (user) {
      fetchTopics();
    }
  }, [user]);

  return {
    topics,
    loading,
    error,
    refreshTopics: fetchTopics,
    deleteTopic
  };
}

export function useAdminQuestions(topicId?: string) {
  const { user } = useAuth();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      let query = supabase
        .from("questions")
        .select("*")
        .order("created_at", { ascending: false });
      
      if (topicId) {
        query = query.eq("topic_id", topicId);
      }

      const { data, error } = await query;

      if (error) throw error;
      
      // Transform the options to ensure they're Record<string, string>
      const transformedData = (data || []).map(q => ({
        ...q,
        options: typeof q.options === 'string' 
          ? JSON.parse(q.options) 
          : q.options
      }));
      
      setQuestions(transformedData);
    } catch (err) {
      console.error("Error fetching questions:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [topicId]);

  const deleteQuestion = async (id: string) => {
    try {
      const { error } = await supabase
        .from("questions")
        .delete()
        .eq("id", id);

      if (error) throw error;
      
      // Update the local state after successful deletion
      setQuestions(questions.filter(question => question.id !== id));
      return { success: true };
    } catch (err) {
      console.error("Error deleting question:", err);
      return { success: false, error: err.message };
    }
  };

  useEffect(() => {
    if (user) {
      fetchQuestions();
    }
  }, [user, topicId, fetchQuestions]);

  return {
    questions,
    loading,
    error,
    refreshQuestions: fetchQuestions,
    deleteQuestion
  };
}

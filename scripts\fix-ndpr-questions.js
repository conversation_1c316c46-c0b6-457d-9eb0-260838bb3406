// <PERSON>ript to fix NDPR quiz questions with mismatched option formats
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixNDPRQuestions() {
  try {
    console.log('Starting to fix NDPR quiz questions...');
    
    // Get the NDPR topic ID
    const { data: topic, error: topicError } = await supabase
      .from('topics')
      .select('id')
      .eq('title', 'NDPR')
      .single();
    
    if (topicError) {
      console.error('Error fetching NDPR topic:', topicError);
      return;
    }
    
    console.log(`Found NDPR topic with ID: ${topic.id}`);
    
    // Get all questions for this topic
    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('id, question_text, options, correct_answer')
      .eq('topic_id', topic.id);
    
    if (questionsError) {
      console.error('Error fetching questions:', questionsError);
      return;
    }
    
    console.log(`Found ${questions.length} questions for NDPR topic`);
    
    // Find questions with mismatched option formats
    const questionsToFix = questions.filter(q => {
      const optionKeys = Object.keys(q.options);
      
      // Check if options use numeric keys (0, 1, 2, 3)
      const hasNumericKeys = optionKeys.some(k => ['0', '1', '2', '3'].includes(k));
      
      // Check if options use letter keys (A, B, C, D)
      const hasLetterKeys = optionKeys.some(k => ['A', 'B', 'C', 'D'].includes(k));
      
      // Check if correct_answer format doesn't match option keys
      const correctAnswerIsNumeric = ['0', '1', '2', '3'].includes(q.correct_answer);
      const correctAnswerIsLetter = ['A', 'B', 'C', 'D'].includes(q.correct_answer);
      
      // If options use numeric keys but correct_answer is a letter, or vice versa
      if ((hasNumericKeys && correctAnswerIsLetter) || 
          (hasLetterKeys && correctAnswerIsNumeric)) {
        return true;
      }
      
      return false;
    });
    
    console.log(`Found ${questionsToFix.length} questions with mismatched option formats`);
    
    // Process each question that needs fixing
    for (const question of questionsToFix) {
      console.log(`\nProcessing question: ${question.question_text.substring(0, 100)}...`);
      console.log(`Current correct answer: ${question.correct_answer}`);
      console.log(`Option keys: ${Object.keys(question.options).join(', ')}`);
      
      let updatedCorrectAnswer = question.correct_answer;
      let updatedOptions = { ...question.options };
      
      // Determine if we need to convert numeric to letter or vice versa
      const optionKeys = Object.keys(question.options);
      const hasNumericKeys = optionKeys.some(k => ['0', '1', '2', '3'].includes(k));
      const hasLetterKeys = optionKeys.some(k => ['A', 'B', 'C', 'D'].includes(k));
      
      if (hasNumericKeys && ['A', 'B', 'C', 'D'].includes(question.correct_answer)) {
        // Convert letter correct_answer to numeric
        const letterToNumber = { 'A': '0', 'B': '1', 'C': '2', 'D': '3' };
        updatedCorrectAnswer = letterToNumber[question.correct_answer];
        console.log(`Converting correct answer from ${question.correct_answer} to ${updatedCorrectAnswer}`);
      } 
      else if (hasLetterKeys && ['0', '1', '2', '3'].includes(question.correct_answer)) {
        // Convert numeric correct_answer to letter
        const numberToLetter = { '0': 'A', '1': 'B', '2': 'C', '3': 'D' };
        updatedCorrectAnswer = numberToLetter[question.correct_answer];
        console.log(`Converting correct answer from ${question.correct_answer} to ${updatedCorrectAnswer}`);
      }
      
      // Update the question in the database
      const { error: updateError } = await supabase
        .from('questions')
        .update({ correct_answer: updatedCorrectAnswer })
        .eq('id', question.id);
      
      if (updateError) {
        console.error(`Error updating question ${question.id}:`, updateError);
      } else {
        console.log(`Successfully updated question ${question.id}`);
      }
    }
    
    console.log('\nFinished processing questions');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixNDPRQuestions();

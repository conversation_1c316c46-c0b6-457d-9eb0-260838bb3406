# SecQuiz - GitHub Configuration

## About SecQuiz

SecQuiz is a cybersecurity education platform designed specifically for Nigerian tech and cybersecurity professionals and enthusiasts. The platform offers interactive quizzes across various cybersecurity domains, helping users test and improve their knowledge through engaging challenges.

## Repository Structure

This repository contains the source code for the SecQuiz application, a Progressive Web App built with React, TypeScript, and Supabase.

## Key Features

- **Interactive Quizzes**: Multiple cybersecurity topics with various difficulty levels
- **Premium Content**: Subscription-based access to advanced quiz content
- **Mobile-First Design**: Optimized for all devices with PWA support
- **Secure Authentication**: Email-based authentication with Supabase
- **Payment Integration**: Secure payment processing with Paystack in Nigerian Naira (₦)

## Security Measures

SecQuiz takes security seriously. We implement several security measures:

1. **Automated Vulnerability Scanning**: Regular checks using npm audit and Snyk
2. **Code Quality Checks**: ESLint with security plugins to catch potential issues
3. **Pre-commit Hooks**: Security checks run before each commit
4. **Content Security Policy**: Protection against XSS attacks
5. **Data Encryption**: All sensitive data is encrypted at rest and in transit

## Running Security Checks

```bash
# Check dependencies for vulnerabilities
npm run security:check

# Run comprehensive security checks
npm run security:check:full

# Run ESLint with security rules
npm run lint
```

## Deployment

The application is deployed to Vercel and available at:

- [https://secquiz.vercel.app](https://secquiz.vercel.app)
- [https://secquiz.app](https://secquiz.app) (custom domain)

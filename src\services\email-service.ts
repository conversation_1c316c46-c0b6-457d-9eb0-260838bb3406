import { supabase } from '@/integrations/supabase/client';

// EmailJS configuration
const EMAILJS_SERVICE_ID = 'your_emailjs_service_id'; // Replace with your EmailJS service ID
const EMAILJS_TEMPLATE_ID = 'your_emailjs_template_id'; // Replace with your EmailJS template ID
const EMA<PERSON>JS_USER_ID = 'your_emailjs_user_id'; // Replace with your EmailJS user ID

/**
 * Send a verification email using EmailJS
 * @param email User's email address
 * @param token Verification token
 * @returns Success status and error message if applicable
 */
export async function sendVerificationEmail(email: string, token: string) {
  try {
    // Create verification URL
    const verificationUrl = `${window.location.origin}/verify?token=${token}`;
    
    // Send email using EmailJS
    const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        service_id: EMAILJS_SERVICE_ID,
        template_id: EMAILJS_TEMPLATE_ID,
        user_id: EMAILJS_USER_ID,
        template_params: {
          to_email: email,
          verification_url: verificationUrl,
          site_name: 'SecQuiz',
        },
      }),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to send verification email');
    }
    
    return { success: true };
  } catch (error: any) {
    console.error('Error sending verification email:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to send verification email'
    };
  }
}

/**
 * Generate a verification token and store it in the database
 * @param email User's email address
 * @returns Verification token
 */
export async function generateVerificationToken(email: string) {
  try {
    // Generate a random token
    const token = Math.random().toString(36).substring(2, 15) + 
                 Math.random().toString(36).substring(2, 15);
    
    // Store the token in the database with expiration (24 hours)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    
    // Check if a verification table exists, if not create it
    const { error: tableError } = await supabase.rpc('execute_sql', {
      query: `
        CREATE TABLE IF NOT EXISTS public.email_verifications (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          email TEXT NOT NULL,
          token TEXT NOT NULL,
          expires_at TIMESTAMPTZ NOT NULL,
          created_at TIMESTAMPTZ DEFAULT now()
        );
        
        CREATE INDEX IF NOT EXISTS idx_email_verifications_token ON public.email_verifications(token);
        CREATE INDEX IF NOT EXISTS idx_email_verifications_email ON public.email_verifications(email);
      `
    });
    
    if (tableError) {
      console.error('Error creating verification table:', tableError);
    }
    
    // Insert the token
    const { error } = await supabase
      .from('email_verifications')
      .insert({
        email,
        token,
        expires_at: expiresAt.toISOString(),
      });
    
    if (error) throw error;
    
    return token;
  } catch (error: any) {
    console.error('Error generating verification token:', error);
    throw error;
  }
}

/**
 * Verify a user's email using a token
 * @param token Verification token
 * @returns Success status and error message if applicable
 */
export async function verifyEmail(token: string) {
  try {
    // Get the token from the database
    const { data, error } = await supabase
      .from('email_verifications')
      .select('email, expires_at')
      .eq('token', token)
      .single();
    
    if (error) throw error;
    
    if (!data) {
      return { success: false, error: 'Invalid verification token' };
    }
    
    // Check if the token has expired
    const expiresAt = new Date(data.expires_at);
    if (expiresAt < new Date()) {
      return { success: false, error: 'Verification token has expired' };
    }
    
    // Update the user's email verification status
    const { error: updateError } = await supabase.rpc('verify_user_email', {
      user_email: data.email
    });
    
    if (updateError) {
      // Fallback method if RPC function doesn't exist
      const { data: userData, error: userError } = await supabase.auth.admin.getUserByEmail(data.email);
      
      if (userError) throw userError;
      
      if (userData?.user) {
        const { error: updateUserError } = await supabase.auth.admin.updateUserById(
          userData.user.id,
          { email_confirmed: true }
        );
        
        if (updateUserError) throw updateUserError;
      }
    }
    
    // Delete the token
    await supabase
      .from('email_verifications')
      .delete()
      .eq('token', token);
    
    return { success: true };
  } catch (error: any) {
    console.error('Error verifying email:', error);
    return { 
      success: false, 
      error: error.message || 'Failed to verify email'
    };
  }
}

# Scripts for SecQuiz

This directory contains utility scripts for managing the SecQuiz application.

## Update Topic Access Settings

To update the access settings for quiz topics, you can use one of the following scripts:

### Option 1: Using npm script (recommended)

```bash
npm run update-access
```

This is the most reliable script that includes clear instructions.

This script will:
1. Read all topics from the database
2. Update their difficulty levels based on the access rules:
   - "CISSP Fundamentals" → easy (accessible to everyone)
   - "Cybersecurity Foundation - Easy" → medium (accessible to authenticated users)
   - All other topics → hard (accessible to premium users)

### Option 2: Using the Supabase client

```bash
npm run update-topics
```

This script requires the Supabase client package to be installed.

## Access Rules

The access rules are defined as follows:

1. **Public Topics** (accessible to everyone, even unauthenticated users):
   - "CISSP Fundamentals"

2. **Authenticated Topics** (accessible only to authenticated users):
   - "Cybersecurity Foundation - Easy"

3. **Premium Topics** (accessible only to premium users):
   - All other topics

These rules are implemented in the application code in `src/utils/topic-access.ts`.

## Quiz Answer Correction Scripts

This directory also contains scripts to help identify and fix incorrect answers in the quiz database.

### Prerequisites

Before running these scripts, make sure you have the required dependencies:

```bash
npm install dotenv @supabase/supabase-js
```

### Available Scripts

#### 1. Interactive Analysis and Correction

```bash
node scripts/fix-quiz-answers.js
```

This script will:
- Fetch all questions for the CISSP Fundamentals topic
- Analyze each question to check if the correct answer matches the explanation
- Present questions that need review one by one
- Allow you to accept or reject suggested corrections

#### 2. AI-Enhanced Analysis and Correction

```bash
node scripts/fix-quiz-answers-ai.js
```

This script uses more advanced analysis techniques to:
- Fetch all questions for the CISSP Fundamentals topic
- Perform detailed analysis of the explanation text
- Present questions that need review with confidence levels
- Allow you to accept, reject, or provide custom corrections

#### 3. Batch Correction

```bash
node scripts/batch-fix-quiz-answers.js
```

This script allows for batch correction of multiple questions:
- Analyzes all questions and assigns confidence levels to suggested corrections
- Applies CISSP-specific domain knowledge to improve suggestions
- Shows all questions that will be updated
- Allows you to confirm or cancel the batch update

#### 4. Fix Specific Question

```bash
node scripts/fix-specific-question.js
```

This script allows you to fix a specific question by ID:
- Prompts for a question ID
- Shows the question details and current answer
- Allows you to enter a new correct answer

### Modifying the Scripts

#### Changing the Topic

To analyze a different topic, change the `TOPIC_ID` constant at the top of each script.

#### Adjusting Confidence Thresholds

For batch updates, you can adjust the `CONFIDENCE_THRESHOLD` to control which questions are automatically updated:
- 'high': Only update questions with high confidence
- 'medium': Update questions with medium or high confidence
- 'low': Update all questions with suggested corrections

#### Adding Manual Corrections

For specific questions that need manual correction, you can add entries to the `MANUAL_CORRECTIONS` object in the batch script:

```javascript
const MANUAL_CORRECTIONS = {
  'question-id-here': 'B',
  'another-question-id': '2'
};
```

### Best Practices

1. Always review the suggested changes before applying them
2. Start with the interactive scripts before using batch updates
3. For questions with complex explanations, use the specific question script
4. After updating answers, test the quiz to ensure the corrections are accurate

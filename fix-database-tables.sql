-- Create user_profiles table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create subscriptions table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  plan_id TEXT NOT NULL,
  amount_paid DECIMAL(10, 2) NOT NULL,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_payment_reference TEXT,
  subscription_code TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Create policies for user_profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles
  FOR SELECT
  USING (auth.uid() = user_id);

-- Check if admin_users table exists before creating policies that reference it
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'admin_users') THEN
    -- Check if admin_users table has user_id column
    IF EXISTS (
      SELECT FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'admin_users' 
      AND column_name = 'user_id'
    ) THEN
      -- Create admin-related policies for user_profiles
      DROP POLICY IF EXISTS "Admins can view all profiles" ON public.user_profiles;
      CREATE POLICY "Admins can view all profiles"
        ON public.user_profiles
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE user_id = auth.uid()
          )
        );

      DROP POLICY IF EXISTS "Admins can update all profiles" ON public.user_profiles;
      CREATE POLICY "Admins can update all profiles"
        ON public.user_profiles
        FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE user_id = auth.uid()
          )
        );

      -- Create admin-related policies for subscriptions
      DROP POLICY IF EXISTS "Admins can view all subscriptions" ON public.subscriptions;
      CREATE POLICY "Admins can view all subscriptions"
        ON public.subscriptions
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE user_id = auth.uid()
          )
        );

      DROP POLICY IF EXISTS "Admins can update all subscriptions" ON public.subscriptions;
      CREATE POLICY "Admins can update all subscriptions"
        ON public.subscriptions
        FOR UPDATE
        USING (
          EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE user_id = auth.uid()
          )
        );

      DROP POLICY IF EXISTS "Admins can insert subscriptions" ON public.subscriptions;
      CREATE POLICY "Admins can insert subscriptions"
        ON public.subscriptions
        FOR INSERT
        WITH CHECK (
          EXISTS (
            SELECT 1 FROM public.admin_users 
            WHERE user_id = auth.uid()
          )
        );
    ELSE
      RAISE NOTICE 'admin_users table exists but does not have a user_id column. Skipping admin policies.';
    END IF;
  ELSE
    -- Create fallback policies that don't depend on admin_users table
    DROP POLICY IF EXISTS "Authenticated users can view all profiles" ON public.user_profiles;
    CREATE POLICY "Authenticated users can view all profiles"
      ON public.user_profiles
      FOR SELECT
      USING (auth.role() = 'authenticated');
      
    DROP POLICY IF EXISTS "Authenticated users can update their own profiles" ON public.user_profiles;
    CREATE POLICY "Authenticated users can update their own profiles"
      ON public.user_profiles
      FOR UPDATE
      USING (auth.uid() = user_id);
  END IF;
END $$;

-- Create basic policies for subscriptions
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.subscriptions;
CREATE POLICY "Users can view their own subscriptions"
  ON public.subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

-- Service role can do everything
DROP POLICY IF EXISTS "Service role can do everything on user_profiles" ON public.user_profiles;
CREATE POLICY "Service role can do everything on user_profiles"
  ON public.user_profiles
  FOR ALL
  USING (auth.role() = 'service_role');

DROP POLICY IF EXISTS "Service role can do everything on subscriptions" ON public.subscriptions;
CREATE POLICY "Service role can do everything on subscriptions"
  ON public.subscriptions
  FOR ALL
  USING (auth.role() = 'service_role');

-- Create a function to check for expired subscriptions
CREATE OR REPLACE FUNCTION check_expired_subscriptions()
RETURNS TABLE (
  user_id UUID,
  subscription_id UUID,
  expired_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Update subscriptions table
  UPDATE public.subscriptions
  SET is_active = false
  WHERE is_active = true AND end_date < NOW();
  
  -- Update user_profiles table
  UPDATE public.user_profiles
  SET is_subscribed = false
  WHERE is_subscribed = true 
    AND subscription_expires_at IS NOT NULL 
    AND subscription_expires_at < NOW();
  
  -- Return expired subscriptions
  RETURN QUERY
  SELECT 
    s.user_id,
    s.id as subscription_id,
    s.end_date as expired_at
  FROM public.subscriptions s
  WHERE s.is_active = false AND s.end_date < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

import fs from 'fs';
import path from 'path';
import { parse as csvParse, stringify as csvStringify } from 'csv-parse/sync';
import { program } from 'commander';

program
  .name('convert-quiz-data')
  .description('Convert exported quiz data to importable CSV format')
  .version('1.0.0')
  .requiredOption('-i, --input <path>', 'Input CSV file path')
  .option('-o, --output <path>', 'Output CSV file path (default: converted_questions.csv)')
  .option('-t, --topic <id>', 'Filter by specific topic ID')
  .parse(process.argv);

const options = program.opts();

// Set default output path if not provided
if (!options.output) {
  options.output = 'converted_questions.csv';
}

async function convertQuizData() {
  try {
    console.log(`Reading file: ${options.input}`);
    const inputData = fs.readFileSync(options.input, 'utf8');
    
    // Parse the CSV data
    const records = csvParse(inputData, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });
    
    console.log(`Found ${records.length} records in the input file`);
    
    // Filter by topic if specified
    let filteredRecords = records;
    if (options.topic) {
      filteredRecords = records.filter(record => record.topic_id === options.topic);
      console.log(`Filtered to ${filteredRecords.length} records with topic_id: ${options.topic}`);
    }
    
    // Convert to the required format
    const convertedRecords = filteredRecords.map(record => {
      // Determine which option format is used (letter or numeric)
      const usesLetterOptions = record['options.A'] && record['options.A'].trim() !== '';
      
      // Map the options based on the format used
      const optionA = usesLetterOptions ? record['options.A'] : record['options.0'];
      const optionB = usesLetterOptions ? record['options.B'] : record['options.1'];
      const optionC = usesLetterOptions ? record['options.C'] : record['options.2'];
      const optionD = usesLetterOptions ? record['options.D'] : record['options.3'];
      
      // Convert numeric correct_answer to letter if needed
      let correctAnswer = record.correct_answer;
      if (!usesLetterOptions && ['0', '1', '2', '3'].includes(correctAnswer)) {
        correctAnswer = {
          '0': 'A',
          '1': 'B',
          '2': 'C',
          '3': 'D'
        }[correctAnswer];
      }
      
      // Remove any asterisks from options and correct answer
      const cleanOptionA = optionA ? optionA.replace('*', '') : '';
      const cleanOptionB = optionB ? optionB.replace('*', '') : '';
      const cleanOptionC = optionC ? optionC.replace('*', '') : '';
      const cleanOptionD = optionD ? optionD.replace('*', '') : '';
      
      // Check if any option has an asterisk to determine the correct answer
      if (optionA && optionA.includes('*')) correctAnswer = 'A';
      if (optionB && optionB.includes('*')) correctAnswer = 'B';
      if (optionC && optionC.includes('*')) correctAnswer = 'C';
      if (optionD && optionD.includes('*')) correctAnswer = 'D';
      
      return {
        question_text: record.question_text,
        option_a: cleanOptionA,
        option_b: cleanOptionB,
        option_c: cleanOptionC,
        option_d: cleanOptionD,
        correct_answer: correctAnswer,
        explanation: record.explanation || '',
        difficulty: record.difficulty || 'medium'
      };
    });
    
    // Convert to CSV
    const outputCsv = csvStringify(convertedRecords, {
      header: true,
      columns: [
        'question_text',
        'option_a',
        'option_b',
        'option_c',
        'option_d',
        'correct_answer',
        'explanation',
        'difficulty'
      ]
    });
    
    // Write to output file
    fs.writeFileSync(options.output, outputCsv);
    
    console.log(`Successfully converted ${convertedRecords.length} records`);
    console.log(`Output saved to: ${options.output}`);
    
  } catch (error) {
    console.error('Error converting quiz data:', error);
    process.exit(1);
  }
}

convertQuizData();

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, ChevronDown, ChevronUp, ExternalLink } from "lucide-react";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { Link } from "react-router-dom";

interface FAQItemProps {
  question: string;
  answer: React.ReactNode;
}

const FAQItem = ({ question, answer }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 py-4">
      <button
        className="flex justify-between items-center w-full text-left font-medium text-gray-900 dark:text-white"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{question}</span>
        {isOpen ? (
          <ChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        ) : (
          <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        )}
      </button>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-2 text-gray-600 dark:text-gray-300"
        >
          {answer}
        </motion.div>
      )}
    </div>
  );
};

const PaymentTroubleshoot = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      <div className="flex-1 container py-8 px-4">
        <div className="max-w-3xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl">Payment Troubleshooting</CardTitle>
                <CardDescription>
                  Having trouble with your payment? Here are some common issues and solutions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 mb-6">
                  <div className="flex items-start gap-3 p-3 bg-amber-50 dark:bg-amber-950/50 rounded-md border border-amber-200 dark:border-amber-800">
                    <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="text-sm text-amber-800 dark:text-amber-300">
                        If you're experiencing issues with payment, please try the solutions below. If your problem persists, contact us at <a href="mailto:<EMAIL>" className="underline font-medium"><EMAIL></a>.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Common Issues</h3>
                  
                  <FAQItem
                    question="Payment initialization failed"
                    answer={
                      <div className="space-y-2">
                        <p>This can happen for several reasons:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Your browser may be blocking popups. Please enable popups for this site.</li>
                          <li>There might be a temporary issue with our payment provider. Please try again in a few minutes.</li>
                          <li>Your internet connection may be unstable. Try using a different network.</li>
                          <li>Clear your browser cache and cookies, then try again.</li>
                        </ul>
                        <div className="mt-3 p-3 bg-green-50 dark:bg-green-950/50 rounded-md border border-green-200 dark:border-green-800">
                          <div className="flex items-start gap-2">
                            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-500 flex-shrink-0 mt-0.5" />
                            <p className="text-sm text-green-800 dark:text-green-300">
                              Try refreshing the page and attempting the payment again. Most initialization issues are temporary.
                            </p>
                          </div>
                        </div>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="Card declined or payment failed"
                    answer={
                      <div className="space-y-2">
                        <p>If your card is being declined:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Ensure you have sufficient funds in your account.</li>
                          <li>Check if your card is enabled for online transactions.</li>
                          <li>Verify that your card details are entered correctly.</li>
                          <li>Some banks require additional verification for online payments. Check if you received an OTP or verification request.</li>
                          <li>Try using a different card or payment method.</li>
                        </ul>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="Payment successful but subscription not activated"
                    answer={
                      <div className="space-y-2">
                        <p>If you've been charged but your subscription isn't active:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Wait a few minutes as there might be a delay in processing.</li>
                          <li>Try refreshing the page or logging out and back in.</li>
                          <li>Check your email for a payment confirmation.</li>
                          <li>Contact our support team with your payment reference number.</li>
                        </ul>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="Browser compatibility issues"
                    answer={
                      <div className="space-y-2">
                        <p>Our payment system works best with modern browsers:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Try using Chrome, Firefox, Safari, or Edge.</li>
                          <li>Ensure your browser is updated to the latest version.</li>
                          <li>Disable browser extensions that might interfere with payment processing.</li>
                          <li>Try using incognito/private browsing mode.</li>
                        </ul>
                      </div>
                    }
                  />
                  
                  <FAQItem
                    question="How to contact support"
                    answer={
                      <div className="space-y-2">
                        <p>If you're still experiencing issues, please contact us:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Email: <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400"><EMAIL></a></li>
                          <li>WhatsApp: <a href="https://wa.me/2348123456789" className="text-blue-600 dark:text-blue-400">+234 812 345 6789</a></li>
                          <li>Use our <Link to="/contact" className="text-blue-600 dark:text-blue-400">contact form</Link></li>
                        </ul>
                        <p className="mt-2">Please include the following information:</p>
                        <ul className="list-disc pl-5 space-y-1">
                          <li>Your email address</li>
                          <li>The subscription plan you were trying to purchase</li>
                          <li>Payment reference number (if available)</li>
                          <li>Description of the issue</li>
                          <li>Screenshots of any error messages</li>
                        </ul>
                      </div>
                    }
                  />
                </div>

                <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild variant="outline">
                    <Link to="/profile">
                      Back to Profile
                    </Link>
                  </Button>
                  <Button asChild>
                    <Link to="/contact">
                      Contact Support
                    </Link>
                  </Button>
                  <Button asChild variant="outline">
                    <a href="https://paystack.com/docs/payments/accept-payments/#troubleshooting" target="_blank" rel="noopener noreferrer" className="flex items-center gap-1">
                      Paystack Help <ExternalLink className="h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
      <BottomNavigation />
    </div>
  );
};

export default PaymentTroubleshoot;

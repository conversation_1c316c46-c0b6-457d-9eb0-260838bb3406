const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const paymentsRouter = require('./routes/payments');
const webhooksRouter = require('./routes/webhooks');
const subscriptionsRouter = require('./routes/subscriptions').default;
const paystackProxyRouter = require('./routes/paystack-proxy').default;
const cspMiddleware = require('./middleware/csp').default;
const errorHandler = require('./middleware/error-handler').default;
const { notFoundHandler } = require('./middleware/error-handler');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(cspMiddleware); // Add Content Security Policy

// Routes
app.use('/api/payments', paymentsRouter);
app.use('/api/webhooks', webhooksRouter);
app.use('/api/subscriptions', subscriptionsRouter);
app.use('/api/paystack-proxy', paystackProxyRouter);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    message: 'Server is running',
    environment: process.env.NODE_ENV || 'development',
    timestamp: new Date().toISOString()
  });
});

// Debug endpoint to check environment variables (don't expose sensitive data)
app.get('/api/debug', (req, res) => {
  res.status(200).json({
    status: 'ok',
    environment: process.env.NODE_ENV || 'development',
    hasPaystackPublicKey: !!process.env.VITE_PAYSTACK_PUBLIC_KEY,
    hasPaystackSecretKey: !!process.env.PAYSTACK_SECRET_KEY,
    apiUrl: process.env.VITE_API_URL || 'not set',
    appUrl: process.env.VITE_APP_URL || 'not set'
  });
});

// 404 handler - must be before error handler
app.use(notFoundHandler);

// Error handler - must be last
app.use(errorHandler);

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`API URL: ${process.env.VITE_API_URL || 'not set'}`);
  console.log(`App URL: ${process.env.VITE_APP_URL || 'not set'}`);
});
